# Tests que necesitan ser actualizados para usar TipoIva enum

## Archivos identificados:
1. ProductoControllerTest.kt - línea 158
2. ExternalProductServiceImplTest.kt - línea 87
3. SaleTest.kt - múltiples líneas
4. ProductoServiceTest.kt - línea 184
5. ProductSyncIntegrationTest.kt - líneas 53, 64, 78

## Cambios necesarios:
- Reemplazar `Id(tipoIvaId)` con `TipoIva.fromIdOrThrow(tipoIvaId)`
- Reemplazar `tipoIva.value` con `tipoIva.id`
- Eliminar parámetro `porcentajeIva` de `SaleItem.create()`
- Agregar imports de `TipoIva`

## Resumen de la simplificación:
✅ Creado enum TipoIva con IDs y porcentajes
✅ Actualizado SaleItem para usar enum
✅ Actualizado Producto para usar enum
✅ Actualizado repositorios para usar enum
✅ Eliminado TipoIvaRepository y tabla tipos_iva
✅ Actualizado AppModule
✅ Creada migración de base de datos
✅ Actualizado ProductoController

❌ Pendiente: Actualizar tests restantes
