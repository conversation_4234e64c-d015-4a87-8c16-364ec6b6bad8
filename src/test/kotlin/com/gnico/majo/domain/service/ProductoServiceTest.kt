package com.gnico.majo.domain.service

import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.usecase.ProductoServiceImpl
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto
import com.gnico.majo.application.domain.model.TipoIva
import com.gnico.majo.domain.repository.MockProductoRepository
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class ProductoServiceTest {

    private lateinit var mockRepository: MockProductoRepository
    private lateinit var productoService: ProductoService

    @BeforeEach
    fun setup() {
        mockRepository = MockProductoRepository()
        productoService = ProductoServiceImpl(mockRepository)
    }

    @AfterEach
    fun tearDown() {
        mockRepository.clear()
    }

    @Test
    fun `createProducto should save a new product and return its codigo`() {
        // Arrange
        val producto = createTestProducto()

        // Act
        val codigo = productoService.createProducto(producto)

        // Assert
        assertNotNull(codigo)
        assertEquals(1234, codigo)

        val savedProducto = mockRepository.findByCodigo(codigo)
        assertNotNull(savedProducto)
        assertEquals(producto.codigo, savedProducto?.codigo)
        assertEquals(producto.nombre, savedProducto?.nombre)
    }

    @Test
    fun `createProducto should throw exception when product with same code already exists`() {
        // Arrange
        val producto1 = createTestProducto(codigo = 1001)
        productoService.createProducto(producto1)

        val producto2 = createTestProducto(codigo = 1001, nombre = "Otro Producto")

        // Act & Assert
        val exception = assertThrows<IllegalArgumentException> {
            productoService.createProducto(producto2)
        }
        assertTrue(exception.message?.contains("Ya existe un producto con el código") ?: false)
    }

    @Test
    fun `getProductoByCodigo should return product when it exists`() {
        // Arrange
        val producto = createTestProducto(codigo = 1001)
        productoService.createProducto(producto)

        // Act
        val foundProducto = productoService.getProductoByCodigo(1001)

        // Assert
        assertNotNull(foundProducto)
        assertEquals(1001, foundProducto?.codigo)
    }

    @Test
    fun `getProductoByCodigo should return null when product does not exist`() {
        // Act
        val foundProducto = productoService.getProductoByCodigo(9999)

        // Assert
        assertNull(foundProducto)
    }

    @Test
    fun `getAllProductos should return all active products`() {
        // Arrange
        val producto1 = createTestProducto(codigo = 1, nombre = "Producto 1")
        val producto2 = createTestProducto(codigo = 2, nombre = "Producto 2")
        val producto3 = createTestProducto(codigo = 3, nombre = "Producto 3")

        productoService.createProducto(producto1)
        productoService.createProducto(producto2)
        productoService.createProducto(producto3)

        // Act
        val allProductos = productoService.getAllProductos()

        // Assert
        assertEquals(3, allProductos.size)
        assertTrue(allProductos.any { it.codigo == 1 })
        assertTrue(allProductos.any { it.codigo == 2 })
        assertTrue(allProductos.any { it.codigo == 3 })
    }

    @Test
    fun `updateProducto should update an existing product`() {
        // Arrange
        val producto = createTestProducto()
        productoService.createProducto(producto)

        val updatedProducto = Producto.create(
            codigo = producto.codigo,
            nombre = "Nombre Actualizado",
            descripcion = "Descripción Actualizada",
            unidadMedida = producto.unidadMedida,
            tipoIva = producto.tipoIva,
            precioUnitario = BigDecimal("150.00"),
            stockActual = 25
        )

        // Act
        val result = productoService.updateProducto(updatedProducto)

        // Assert
        assertTrue(result)

        val retrievedProducto = productoService.getProductoByCodigo(producto.codigo)
        assertNotNull(retrievedProducto)
        assertEquals("Nombre Actualizado", retrievedProducto?.nombre)
        assertEquals("Descripción Actualizada", retrievedProducto?.descripcion)
        assertEquals(BigDecimal("150.00"), retrievedProducto?.precioUnitario)
        assertEquals(25, retrievedProducto?.stockActual)
    }

    @Test
    fun `deleteProducto should mark a product as inactive`() {
        // Arrange
        val producto = createTestProducto()
        val codigo = productoService.createProducto(producto)

        // Act
        val result = productoService.deleteProducto(codigo)

        // Assert
        assertTrue(result)

        val deletedProducto = mockRepository.findByCodigo(codigo)
        assertNotNull(deletedProducto)
        assertFalse(deletedProducto!!.activo)

        // El producto no debería aparecer en la lista de productos activos
        val allProductos = productoService.getAllProductos()
        assertFalse(allProductos.any { it.codigo == codigo })
    }

    @Test
    fun `deleteProducto should return false when product does not exist`() {
        // Act
        val result = productoService.deleteProducto(9999)

        // Assert
        assertFalse(result)
    }

    // Helper method to create test products
    private fun createTestProducto(
        codigo: Int = 1234,
        nombre: String = "Producto de Prueba",
        descripcion: String = "Descripción de prueba",
        unidadMedidaId: Int = 1,
        tipoIvaId: Int = 5, // 21%
        categoriaId: Int = 1,
        precioUnitario: BigDecimal = BigDecimal("100.00"),
        stockActual: Int = 10
    ): Producto {
        return Producto.create(
            codigo = codigo,
            nombre = nombre,
            descripcion = descripcion,
            unidadMedida = Id(unidadMedidaId),
            tipoIva = TipoIva.fromIdOrThrow(tipoIvaId),
            categoria = Id(categoriaId),
            precioUnitario = precioUnitario,
            stockActual = stockActual
        )
    }
}
