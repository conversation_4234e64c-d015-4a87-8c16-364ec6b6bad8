package com.gnico.majo.domain.model

import com.gnico.majo.application.domain.model.SaleItem
import com.gnico.majo.application.domain.model.TipoIva
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import kotlin.test.assertEquals

class SaleItemTest {

    @Test
    fun `should create sale item with correct calculations`() {
        // Given
        val productoCodigo = 1234
        val cantidad = BigDecimal("2.5")
        val precioUnitario = BigDecimal("100.00")
        val tipoIva = TipoIva.IVA_21

        // When
        val saleItem = SaleItem.create(
            productoCodigo = productoCodigo,
            cantidad = cantidad,
            precioUnitario = precioUnitario,
            tipoIva = tipoIva
        )

        // Then
        assertEquals(productoCodigo, saleItem.productoCodigo)
        assertEquals(cantidad, saleItem.cantidad)
        assertEquals(precioUnitario, saleItem.precioUnitario)
        assertEquals(tipoIva, saleItem.tipoIva)
        assertEquals(0, BigDecimal("250.00").compareTo(saleItem.subtotal)) // 2.5 * 100
        assertEquals(0, BigDecimal("206.61").compareTo(saleItem.baseImp)) // 250 / 1.21
        assertEquals(0, BigDecimal("43.39").compareTo(saleItem.importeIva)) // 250 - 206.61
        assertEquals(0, BigDecimal("250.00").compareTo(saleItem.totalWithTax())) // subtotal
    }

    @Test
    fun `should fail when creating sale item with zero quantity`() {
        // When & Then
        assertThrows<IllegalArgumentException> {
            SaleItem.create(
                productoCodigo = 1234,
                cantidad = BigDecimal.ZERO,
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        }
    }

    @Test
    fun `should fail when creating sale item with negative quantity`() {
        // When & Then
        assertThrows<IllegalArgumentException> {
            SaleItem.create(
                productoCodigo = 1234,
                cantidad = BigDecimal("-1.0"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        }
    }

    @Test
    fun `should fail when creating sale item with negative price`() {
        // When & Then
        assertThrows<IllegalArgumentException> {
            SaleItem.create(
                productoCodigo = 1234,
                cantidad = BigDecimal("1.0"),
                precioUnitario = BigDecimal("-100.00"),
                tipoIva = TipoIva.IVA_21
            )
        }
    }



    @Test
    fun `should handle zero tax percentage correctly`() {
        // Given
        val productoCodigo = 1234
        val cantidad = BigDecimal("1.0")
        val precioUnitario = BigDecimal("100.00")
        val tipoIva = TipoIva.EXENTO

        // When
        val saleItem = SaleItem.create(
            productoCodigo = productoCodigo,
            cantidad = cantidad,
            precioUnitario = precioUnitario,
            tipoIva = tipoIva
        )

        // Then
        assertEquals(0, BigDecimal("100.00").compareTo(saleItem.subtotal))
        assertEquals(0, BigDecimal("100.00").compareTo(saleItem.baseImp))
        assertEquals(0, BigDecimal.ZERO.compareTo(saleItem.importeIva))
        assertEquals(0, BigDecimal("100.00").compareTo(saleItem.totalWithTax()))
    }

    @Test
    fun `should handle zero price correctly`() {
        // Given
        val productoCodigo = 1234
        val cantidad = BigDecimal("1.0")
        val precioUnitario = BigDecimal.ZERO
        val tipoIva = TipoIva.IVA_21

        // When
        val saleItem = SaleItem.create(
            productoCodigo = productoCodigo,
            cantidad = cantidad,
            precioUnitario = precioUnitario,
            tipoIva = tipoIva
        )

        // Then
        assertEquals(0, BigDecimal.ZERO.compareTo(saleItem.subtotal))
        assertEquals(0, BigDecimal.ZERO.compareTo(saleItem.baseImp))
        assertEquals(0, BigDecimal.ZERO.compareTo(saleItem.importeIva))
        assertEquals(0, BigDecimal.ZERO.compareTo(saleItem.totalWithTax()))
    }
}
