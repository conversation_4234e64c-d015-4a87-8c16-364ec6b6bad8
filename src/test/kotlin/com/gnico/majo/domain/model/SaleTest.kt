package com.gnico.majo.domain.model

import com.gnico.majo.application.domain.model.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class SaleTest {

    @Test
    fun `should create sale with valid data`() {
        // Given
        val cliente = Cliente(Id(1), "Test Cliente", "12345678901")
        val usuario = Usuario("user1", "Usuario Test", "Usuario Test", true)
        val items = listOf(
            SaleItem.create(
                productoCodigo = 1234,
                cantidad = BigDecimal("2.0"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        // When
        val sale = Sale.create(
            cliente = cliente,
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO"
        )

        // Then
        assertEquals(cliente, sale.cliente)
        assertEquals(usuario, sale.usuario)
        assertEquals(items, sale.items)
        assertEquals("EFECTIVO", sale.medioPago)
        assertFalse(sale.comprobanteEmitido)
        assertTrue(sale.numeroVenta.startsWith("V-"))
        assertEquals(0, BigDecimal("200.00").compareTo(sale.montoTotal)) // Precio con IVA incluido
    }

    @Test
    fun `should fail when creating sale with empty items`() {
        // Given
        val usuario = Usuario("user1", "Usuario Test", "Usuario Test", true)
        val items = emptyList<SaleItem>()

        // When & Then
        assertThrows<IllegalArgumentException> {
            Sale.create(
                cliente = null,
                usuario = usuario,
                items = items,
                medioPago = "EFECTIVO"
            )
        }
    }

    @Test
    fun `should fail when creating sale with invalid medio pago`() {
        // Given
        val usuario = Usuario("user1", "Usuario Test", "Usuario Test", true)
        val items = listOf(
            SaleItem.create(
                productoCodigo = 1234,
                cantidad = BigDecimal("1.0"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        // When & Then
        assertThrows<IllegalArgumentException> {
            Sale.create(
                cliente = null,
                usuario = usuario,
                items = items,
                medioPago = "MEDIO_INVALIDO"
            )
        }
    }

    @Test
    fun `should validate comprobante creation correctly`() {
        // Given
        val cliente = Cliente(Id(1), "Test Cliente", "12345678901")
        val usuario = Usuario("user1", "Usuario Test", "Usuario Test", true)
        val items = listOf(
            SaleItem.create(
                productoCodigo = 1234,
                cantidad = BigDecimal("1.0"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        val saleWithCliente = Sale.create(cliente, usuario, items, "EFECTIVO")
        val saleWithoutCliente = Sale.create(null, usuario, items, "EFECTIVO")

        // When & Then
        assertTrue(saleWithCliente.canCreateComprobante("FACTURA_A"))
        assertTrue(saleWithCliente.canCreateComprobante("FACTURA_B"))
        
        assertFalse(saleWithoutCliente.canCreateComprobante("FACTURA_A"))
        assertTrue(saleWithoutCliente.canCreateComprobante("FACTURA_B"))

        // Should not throw
        saleWithCliente.validateComprobanteCreation("FACTURA_A")
        saleWithoutCliente.validateComprobanteCreation("FACTURA_B")

        // Should throw
        assertThrows<IllegalArgumentException> {
            saleWithoutCliente.validateComprobanteCreation("FACTURA_A")
        }
    }

    @Test
    fun `should calculate tax amounts correctly`() {
        // Given
        val usuario = Usuario("user1", "Usuario Test", "Usuario Test", true)
        val items = listOf(
            SaleItem.create(
                productoCodigo = 1234,
                cantidad = BigDecimal("2.0"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            ),
            SaleItem.create(
                productoCodigo = 5678,
                cantidad = BigDecimal("1.0"),
                precioUnitario = BigDecimal("50.00"),
                tipoIva = TipoIva.IVA_10_5
            )
        )

        val sale = Sale.create(null, usuario, items, "EFECTIVO")

        // When
        val taxAmounts = sale.calculateTaxAmounts()

        // Then
        // Con precios que incluyen IVA: 200 + 50 = 250 total
        // Base imponible: 200/1.21 + 50/1.105 ≈ 165.29 + 45.25 = 210.54
        // IVA: 250 - 210.54 = 39.46
        assertEquals(0, BigDecimal("210.54").compareTo(taxAmounts.impNeto.setScale(2, java.math.RoundingMode.HALF_UP)))
        assertEquals(0, BigDecimal("39.46").compareTo(taxAmounts.impIva.setScale(2, java.math.RoundingMode.HALF_UP)))
        assertEquals(0, BigDecimal("250.00").compareTo(taxAmounts.impTotal))
        assertEquals(0, BigDecimal.ZERO.compareTo(taxAmounts.impTotConc))
        assertEquals(0, BigDecimal.ZERO.compareTo(taxAmounts.impTrib))
    }
}
