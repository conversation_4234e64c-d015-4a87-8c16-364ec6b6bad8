package com.gnico.majo.application.usecase

import com.gnico.majo.adapter.persistence.MockExternalProductRepository
import com.gnico.majo.application.domain.model.ExternalProduct
import com.gnico.majo.domain.repository.MockProductoRepository
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class ExternalProductServiceImplTest {

    private lateinit var externalProductService: ExternalProductServiceImpl
    private lateinit var mockExternalRepository: MockExternalProductRepository
    private lateinit var mockProductoRepository: MockProductoRepository

    @BeforeEach
    fun setUp() {
        mockExternalRepository = MockExternalProductRepository()
        mockProductoRepository = MockProductoRepository()
        externalProductService = ExternalProductServiceImpl(
            mockExternalRepository,
            mockProductoRepository
        )
    }

    @Test
    fun `getAllExternalProducts should return all products`() = runBlocking {
        // When
        val products = externalProductService.getAllExternalProducts()

        // Then
        assertEquals(4, products.size)
        assertEquals("Producto Externo 0", products[0].name)
        assertEquals(0, products[0].productId)
        assertEquals("Descripción del producto 0", products[0].description)
        assertEquals("5.25", products[0].price)
        assertEquals(1, products[0].uomId)
        assertEquals(true, products[0].isActive)
    }

    @Test
    fun `getExternalProductById should return product when exists`() = runBlocking {
        // When
        val product = externalProductService.getExternalProductById(0)

        // Then
        assertNotNull(product)
        assertEquals(0, product.productId)
        assertEquals("Producto Externo 0", product.name)
        assertEquals("Descripción del producto 0", product.description)
        assertEquals("5.25", product.price)
        assertEquals(1, product.uomId)
        assertEquals(true, product.isActive)
    }

    @Test
    fun `getExternalProductById should return null when product does not exist`() = runBlocking {
        // When
        val product = externalProductService.getExternalProductById(999)

        // Then
        assertNull(product)
    }

    @Test
    fun `syncExternalProducts should clear internal products and sync external products`() = runBlocking {
        // Given - agregar algunos productos internos primero
        mockProductoRepository.clear()

        // When
        val count = externalProductService.syncExternalProducts()

        // Then
        assertEquals(4, count)

        // Verificar que los productos se sincronizaron correctamente
        val productosInternos = mockProductoRepository.findAll()
        assertEquals(3, productosInternos.size) // Solo productos activos

        val producto0 = productosInternos.find { it.codigo == 0 }
        assertNotNull(producto0)
        assertEquals("Producto Externo 0", producto0.nombre)
        assertEquals("Descripción del producto 0", producto0.descripcion)
        assertEquals(5, producto0.tipoIva.id) // Tipo IVA fijo (21%)
        assertEquals(1, producto0.unidadMedida.value)
        assertEquals(true, producto0.activo)
        assertEquals(null, producto0.stockActual) // Stock debe ser null
    }
}
