package com.gnico.majo.integration

import com.gnico.majo.adapter.persistence.MockExternalProductRepository
import com.gnico.majo.application.usecase.ExternalProductServiceImpl
import com.gnico.majo.application.usecase.ProductoServiceImpl
import com.gnico.majo.domain.repository.MockProductoRepository
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class ProductSyncIntegrationTest {

    private lateinit var externalProductService: ExternalProductServiceImpl
    private lateinit var productoService: ProductoServiceImpl
    private lateinit var mockExternalRepository: MockExternalProductRepository
    private lateinit var mockProductoRepository: MockProductoRepository

    @BeforeEach
    fun setUp() {
        mockExternalRepository = MockExternalProductRepository()
        mockProductoRepository = MockProductoRepository()
        
        externalProductService = ExternalProductServiceImpl(
            mockExternalRepository,
            mockProductoRepository
        )
        
        productoService = ProductoServiceImpl(mockProductoRepository)
    }

    @Test
    fun `should sync external products to internal table successfully`() = runBlocking {
        // Given - tabla interna vacía
        assertEquals(0, productoService.getAllProductos().size)
        
        // When - sincronizar productos externos
        val syncedCount = externalProductService.syncExternalProducts()
        
        // Then - verificar sincronización
        assertEquals(4, syncedCount)

        // Verificar productos internos sincronizados (solo activos)
        val productosInternos = productoService.getAllProductos()
        assertEquals(3, productosInternos.size) // Solo productos activos

        // Verificar mapeo correcto del producto con código 0
        val producto0 = productosInternos.find { it.codigo == 0 }
        assertNotNull(producto0)
        assertEquals("Producto Externo 0", producto0.nombre)
        assertEquals("Descripción del producto 0", producto0.descripcion)
        assertEquals(5, producto0.tipoIva.id) // Tipo IVA fijo (21%)
        assertEquals(1, producto0.unidadMedida.value)
        assertEquals(true, producto0.activo)
        assertEquals("5.25", producto0.precioUnitario.toString())
        assertEquals(null, producto0.stockActual) // Stock debe ser null

        // Verificar mapeo correcto del segundo producto
        val producto2 = productosInternos.find { it.codigo == 2 }
        assertNotNull(producto2)
        assertEquals("Producto Externo 2", producto2.nombre)
        assertEquals("Descripción del producto 2", producto2.descripcion)
        assertEquals(5, producto2.tipoIva.id) // Tipo IVA fijo (21%)
        assertEquals(2, producto2.unidadMedida.value)
        assertEquals(true, producto2.activo)
        assertEquals("25.75", producto2.precioUnitario.toString())
        assertEquals(null, producto2.stockActual) // Stock debe ser null
    }

    @Test
    fun `should clear existing products before sync`() = runBlocking {
        // Given - agregar algunos productos internos primero
        val productoExistente = com.gnico.majo.application.domain.model.Producto.create(
            codigo = 9999,
            nombre = "Producto Existente",
            unidadMedida = com.gnico.majo.application.domain.model.Id(1),
            tipoIva = com.gnico.majo.application.domain.model.TipoIva.fromIdOrThrow(5)
        )
        productoService.createProducto(productoExistente)
        assertEquals(1, productoService.getAllProductos().size)
        
        // When - sincronizar productos externos
        val syncedCount = externalProductService.syncExternalProducts()
        
        // Then - verificar que se limpiaron los productos existentes
        assertEquals(4, syncedCount)
        val productosInternos = productoService.getAllProductos()
        assertEquals(3, productosInternos.size) // Solo productos sincronizados activos
        
        // Verificar que el producto existente ya no está
        val productoExistenteEncontrado = productosInternos.find { it.codigo == 9999 }
        assertEquals(null, productoExistenteEncontrado)
    }
}
