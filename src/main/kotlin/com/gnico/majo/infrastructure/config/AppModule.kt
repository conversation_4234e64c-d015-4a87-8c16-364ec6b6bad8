package com.gnico.majo.infrastructure.config

import com.gnico.majo.adapter.persistence.JooqClienteRepository
import com.gnico.majo.adapter.persistence.JooqProductoRepository
import com.gnico.majo.adapter.persistence.JooqSaleRepository
import com.gnico.majo.adapter.persistence.JooqSalesReportAdapter

import com.gnico.majo.adapter.persistence.JooqUsuarioRepository
import com.gnico.majo.adapter.persistence.JdbcExternalProductRepository
import com.gnico.majo.adapter.persistence.JdbcExternalSaleRepository
import com.gnico.majo.adapter.persistence.JdbcExternalUserRepository
import com.gnico.majo.adapter.printer.EscPosPrinterAdapter
import com.gnico.majo.adapter.afip.AfipServiceAdapter
import com.gnico.majo.adapter.persistence.JooqWsaaCredentialsRepository
import com.gnico.majo.adapter.controller.rest.ProductoController
import com.gnico.majo.adapter.controller.rest.SaleController
import com.gnico.majo.adapter.controller.rest.ExternalProductController
import com.gnico.majo.adapter.controller.rest.UsuarioController
import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.port.`in`.SaleService
import com.gnico.majo.application.port.`in`.ExternalProductService
import com.gnico.majo.application.port.`in`.UsuarioService
import com.gnico.majo.application.port.out.PrinterPort
import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.application.port.out.ProductoRepositoryPort
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.WsaaCredentialsRepository
import com.gnico.majo.application.port.out.ClienteRepository

import com.gnico.majo.application.port.out.UsuarioRepository
import com.gnico.majo.application.port.out.ExternalProductRepositoryPort
import com.gnico.majo.application.port.out.ExternalSaleRepositoryPort
import com.gnico.majo.application.port.out.ExternalUserRepositoryPort
import com.gnico.majo.application.usecase.ProductoServiceImpl
import com.gnico.majo.application.usecase.SaleServiceImpl
import com.gnico.majo.application.usecase.ExternalProductServiceImpl
import com.gnico.majo.application.usecase.UsuarioServiceImpl
import com.gnico.majo.infrastructure.afip.WsaaCredentialsService
import com.gnico.majo.infrastructure.afip.webservices.WsaaClientImpl
import com.gnico.majo.infrastructure.config.AfipConfigurationService
import com.gnico.majo.infrastructure.startup.AfipStartupService
import com.gnico.majo.infrastructure.startup.ApplicationStartup
import org.koin.dsl.module


/**
 * Main application module for dependency injection
 */
val appModule = module {
    // Repositories
    single<SaleRepositoryPort> { JooqSaleRepository() }
    single<UsuarioRepository> { JooqUsuarioRepository() }
    single<ClienteRepository> { JooqClienteRepository() }

    single<SalesReportPort> { JooqSalesReportAdapter() }
    single<ProductoRepositoryPort> { JooqProductoRepository() }
    single<ExternalProductRepositoryPort> { JdbcExternalProductRepository() }
    single<ExternalSaleRepositoryPort> { JdbcExternalSaleRepository() }
    single<ExternalUserRepositoryPort> { JdbcExternalUserRepository() }
    single<WsaaCredentialsRepository> { JooqWsaaCredentialsRepository() }

    // AFIP Configuration and Services
    single<AfipConfigurationService> { AfipConfigurationService() }
    single<WsaaClientImpl> { WsaaClientImpl(get<AfipConfigurationService>().getConfiguration()) }
    single<WsaaCredentialsService> {
        WsaaCredentialsService(
            credentialsRepository = get(),
            wsaaClient = get(),
            configuration = get<AfipConfigurationService>().getConfiguration()
        )
    }
    single<AfipStartupService> { AfipStartupService(get()) }
    single<ApplicationStartup> { ApplicationStartup() }

    // Services
    single<PrinterPort> { EscPosPrinterAdapter() }
    single<AfipService> { AfipServiceAdapter(get()) }
    single<SaleService> {
        SaleServiceImpl(
            saleRepository = get(),
            printer = get(),
            salesReport = get(),
            usuarioRepository = get(),
            clienteRepository = get(),
            externalSaleRepository = get(),
            afipService = get()
        )
    }
    single<ProductoService> { ProductoServiceImpl(productoRepository = get()) }
    single<ExternalProductService> {
        ExternalProductServiceImpl(
            externalProductRepository = get(),
            productoRepository = get()
        )
    }
    single<UsuarioService> {
        UsuarioServiceImpl(
            usuarioRepository = get(),
            externalUserRepository = get()
        )
    }

    // Controllers
    single { SaleController(saleService = get()) }
    single { ProductoController(productoService = get()) }
    single { ExternalProductController(externalProductService = get()) }
    single { UsuarioController(usuarioService = get()) }
}
