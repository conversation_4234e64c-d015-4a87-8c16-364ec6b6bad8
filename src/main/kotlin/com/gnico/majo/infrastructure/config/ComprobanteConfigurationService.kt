package com.gnico.majo.infrastructure.config

import com.gnico.majo.utils.Env

/**
 * Servicio de configuración para comprobantes fiscales
 * Maneja valores por defecto desde variables de entorno
 */
object ComprobanteConfigurationService {
    
    /**
     * Obtiene el punto de venta por defecto desde .env o usa valor por defecto
     */
    fun getDefaultPuntoVenta(): Int {
        return Env.getOrDefault("PUNTO_DE_VENTA", "1").toIntOrNull() ?: 1
    }
    
    /**
     * Obtiene el tipo de comprobante por defecto
     */
    fun getDefaultTipoComprobante(): String {
        return Env.getOrDefault("TIPO_COMPROBANTE_DEFAULT", "FACTURA_B")
    }
    
    /**
     * Valida que el punto de venta sea válido
     */
    fun validatePuntoVenta(puntoVenta: Int): Int {
        require(puntoVenta > 0) { "El punto de venta debe ser mayor a 0" }
        require(puntoVenta <= 9999) { "El punto de venta debe ser menor o igual a 9999" }
        return puntoVenta
    }
    
    /**
     * Valida que el tipo de comprobante sea válido
     */
    fun validateTipoComprobante(tipoComprobante: String): String {
        val validTypes = setOf(
            "FACTURA_A", "FACTURA_B", "FACTURA_C",
            "NOTA_CREDITO_A", "NOTA_CREDITO_B", "NOTA_CREDITO_C",
            "NOTA_DEBITO_A", "NOTA_DEBITO_B", "NOTA_DEBITO_C"
        )
        
        require(tipoComprobante.isNotBlank()) { "El tipo de comprobante no puede estar vacío" }
        require(tipoComprobante in validTypes) { 
            "Tipo de comprobante '$tipoComprobante' no válido. Tipos válidos: ${validTypes.joinToString()}" 
        }
        
        return tipoComprobante
    }
    
    /**
     * Obtiene configuración completa con valores por defecto aplicados
     */
    data class ComprobanteConfig(
        val puntoVenta: Int,
        val tipoComprobante: String
    )
    
    /**
     * Resuelve la configuración aplicando valores por defecto cuando sea necesario
     */
    fun resolveConfig(
        puntoVenta: Int? = null,
        tipoComprobante: String? = null
    ): ComprobanteConfig {
        val resolvedPuntoVenta = puntoVenta ?: getDefaultPuntoVenta()
        val resolvedTipoComprobante = tipoComprobante ?: getDefaultTipoComprobante()
        
        return ComprobanteConfig(
            puntoVenta = validatePuntoVenta(resolvedPuntoVenta),
            tipoComprobante = validateTipoComprobante(resolvedTipoComprobante)
        )
    }
}
