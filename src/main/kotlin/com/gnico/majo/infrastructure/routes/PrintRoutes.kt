package com.gnico.majo.infrastructure.routes

import com.gnico.majo.adapter.controller.rest.PrintController
import com.gnico.majo.adapter.controller.rest.BuscarVentasRequest
import com.gnico.majo.adapter.controller.rest.BuscarComprobantesRequest
import com.gnico.majo.adapter.controller.dto.ErrorResponse
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import io.ktor.server.routing.routing

fun Application.configurePrintRoutes(printController: PrintController) {
    routing {
        route("/api/print") {

            // POST /api/print/ticket-venta/{ventaId} - Imprimir ticket de venta
            post("/ticket-venta/{ventaId}") {
                try {
                    val ventaIdParam = call.parameters["ventaId"]
                    if (ventaIdParam.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId es requerido"))
                        return@post
                    }

                    val ventaId = ventaIdParam.toIntOrNull()
                    if (ventaId == null) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId debe ser un número válido"))
                        return@post
                    }

                    println("🔍 POST /api/print/ticket-venta/$ventaId")
                    val response = printController.imprimirTicketVenta(ventaId)
                    println("✅ Ticket de venta impreso: ${response.message}")
                    call.respond(HttpStatusCode.OK, response)
                    
                } catch (e: Exception) {
                    println("❌ Error al imprimir ticket de venta: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // POST /api/print/ticket-comprobante/{comprobanteId} - Imprimir ticket de comprobante
            post("/ticket-comprobante/{comprobanteId}") {
                try {
                    val comprobanteIdParam = call.parameters["comprobanteId"]
                    if (comprobanteIdParam.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("comprobanteId es requerido"))
                        return@post
                    }

                    val comprobanteId = comprobanteIdParam.toIntOrNull()
                    if (comprobanteId == null) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("comprobanteId debe ser un número válido"))
                        return@post
                    }

                    println("🔍 POST /api/print/ticket-comprobante/$comprobanteId")
                    val response = printController.imprimirTicketComprobante(comprobanteId)
                    println("✅ Ticket de comprobante impreso: ${response.message}")
                    call.respond(HttpStatusCode.OK, response)
                    
                } catch (e: Exception) {
                    println("❌ Error al imprimir ticket de comprobante: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // POST /api/print/comprobante-fiscal/{comprobanteId} - Imprimir comprobante fiscal
            post("/comprobante-fiscal/{comprobanteId}") {
                try {
                    val comprobanteIdParam = call.parameters["comprobanteId"]
                    if (comprobanteIdParam.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("comprobanteId es requerido"))
                        return@post
                    }

                    val comprobanteId = comprobanteIdParam.toIntOrNull()
                    if (comprobanteId == null) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("comprobanteId debe ser un número válido"))
                        return@post
                    }

                    println("🔍 POST /api/print/comprobante-fiscal/$comprobanteId")
                    val response = printController.imprimirComprobanteFiscal(comprobanteId)
                    println("✅ Comprobante fiscal impreso: ${response.message}")
                    call.respond(HttpStatusCode.OK, response)
                    
                } catch (e: Exception) {
                    println("❌ Error al imprimir comprobante fiscal: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // POST /api/print/buscar-ventas - Buscar ventas para reimpresión
            post("/buscar-ventas") {
                try {
                    println("🔍 POST /api/print/buscar-ventas")
                    val request = call.receive<BuscarVentasRequest>()
                    println("🔍 Búsqueda: numero=${request.numeroVenta}, desde=${request.fechaDesde}, hasta=${request.fechaHasta}, usuario=${request.usuario}")
                    
                    val ventas = printController.buscarVentasParaReimpresion(request)
                    println("✅ Encontradas ${ventas.size} ventas")
                    call.respond(HttpStatusCode.OK, ventas)
                    
                } catch (e: IllegalArgumentException) {
                    println("❌ Error de argumento: ${e.message}")
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Invalid request"))
                } catch (e: Exception) {
                    println("❌ Error al buscar ventas: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // POST /api/print/buscar-comprobantes - Buscar comprobantes para reimpresión
            post("/buscar-comprobantes") {
                try {
                    println("🔍 POST /api/print/buscar-comprobantes")
                    val request = call.receive<BuscarComprobantesRequest>()
                    println("🔍 Búsqueda: pv=${request.puntoVenta}, numero=${request.numeroComprobante}, desde=${request.fechaDesde}, hasta=${request.fechaHasta}")
                    
                    val comprobantes = printController.buscarComprobantesParaReimpresion(request)
                    println("✅ Encontrados ${comprobantes.size} comprobantes")
                    call.respond(HttpStatusCode.OK, comprobantes)
                    
                } catch (e: IllegalArgumentException) {
                    println("❌ Error de argumento: ${e.message}")
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Invalid request"))
                } catch (e: Exception) {
                    println("❌ Error al buscar comprobantes: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/print/ventas/{numeroVenta} - Buscar venta específica para reimpresión
            get("/ventas/{numeroVenta}") {
                try {
                    val numeroVenta = call.parameters["numeroVenta"]
                    if (numeroVenta.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("numeroVenta es requerido"))
                        return@get
                    }

                    println("🔍 GET /api/print/ventas/$numeroVenta")
                    val request = BuscarVentasRequest(numeroVenta = numeroVenta)
                    val ventas = printController.buscarVentasParaReimpresion(request)
                    
                    if (ventas.isNotEmpty()) {
                        call.respond(HttpStatusCode.OK, ventas.first())
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Venta no encontrada"))
                    }
                    
                } catch (e: Exception) {
                    println("❌ Error al buscar venta: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/print/comprobantes/{puntoVenta}/{numeroComprobante} - Buscar comprobante específico
            get("/comprobantes/{puntoVenta}/{numeroComprobante}") {
                try {
                    val puntoVentaParam = call.parameters["puntoVenta"]
                    val numeroComprobanteParam = call.parameters["numeroComprobante"]
                    
                    if (puntoVentaParam.isNullOrBlank() || numeroComprobanteParam.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("puntoVenta y numeroComprobante son requeridos"))
                        return@get
                    }

                    val puntoVenta = puntoVentaParam.toIntOrNull()
                    val numeroComprobante = numeroComprobanteParam.toIntOrNull()
                    
                    if (puntoVenta == null || numeroComprobante == null) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("puntoVenta y numeroComprobante deben ser números válidos"))
                        return@get
                    }

                    println("🔍 GET /api/print/comprobantes/$puntoVenta/$numeroComprobante")
                    val request = BuscarComprobantesRequest(
                        puntoVenta = puntoVenta,
                        numeroComprobante = numeroComprobante
                    )
                    val comprobantes = printController.buscarComprobantesParaReimpresion(request)
                    
                    if (comprobantes.isNotEmpty()) {
                        call.respond(HttpStatusCode.OK, comprobantes.first())
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Comprobante no encontrado"))
                    }
                    
                } catch (e: Exception) {
                    println("❌ Error al buscar comprobante: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }
        }
    }
}
