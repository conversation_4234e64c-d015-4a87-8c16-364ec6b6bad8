package com.gnico.majo.adapter.persistence

import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.application.domain.model.Cliente
import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.SaleItem
import com.gnico.majo.application.domain.model.Usuario
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.jooq.generated.tables.Clientes.Companion.CLIENTES
import com.gnico.majo.jooq.generated.tables.Comprobantes.Companion.COMPROBANTES
import com.gnico.majo.jooq.generated.tables.DetallesVenta.Companion.DETALLES_VENTA
import com.gnico.majo.jooq.generated.tables.Usuarios.Companion.USUARIOS
import com.gnico.majo.jooq.generated.tables.Ventas.Companion.VENTAS
import org.jooq.impl.DSL
import java.time.LocalDateTime

class JooqSaleRepository : SaleRepositoryPort {
    override fun saveSale(sale: Sale): Id {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)

            // Guardar venta
            val ventaRecord = ctx.newRecord(VENTAS).apply {
                numeroVenta = sale.numeroVenta
                clienteId = sale.cliente?.id?.value
                usuarioUsername = sale.usuario.username // Cambio de vendedorId a usuarioUsername
                fechaVenta = sale.fechaVenta
                montoTotal = sale.montoTotal
                comprobanteEmitido = sale.comprobanteEmitido
                medioPago = sale.medioPago
                codigoTicketBalanza = sale.codigoTicketBalanza
                idTicketBalanza = sale.idTicketBalanza
                creadoEn = LocalDateTime.now()
            }
            ventaRecord.store()

            // Guardar detalles
            sale.items.forEach { item ->
                ctx.newRecord(DETALLES_VENTA).apply {
                    ventaId = ventaRecord.id
                    productoCodigoRef = item.productoCodigo
                    cantidad = item.cantidad
                    precioUnitario = item.precioUnitario
                    tipoIvaId = item.tipoIva.id
                    subtotal = item.subtotal
                    baseImp = item.baseImp
                    importeIva = item.importeIva
                    creadoEn = LocalDateTime.now()
                }.store()
            }

            Id(ventaRecord.id ?: throw IllegalStateException("Error al guardar la venta"))
        }
    }

    override fun findSaleById(id: Id): Sale? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val saleData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.CLIENTE_ID,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO,
                CLIENTES.RAZON_SOCIAL,
                CLIENTES.CUIT
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .leftJoin(CLIENTES).on(VENTAS.CLIENTE_ID.eq(CLIENTES.ID))
                .where(VENTAS.ID.eq(id.value))
                .fetchOne() ?: return@transactionResult null

            val usuario = Usuario(
                username = saleData[VENTAS.USUARIO_USERNAME]!!,
                nombre = saleData[USUARIOS.NOMBRE]!!,
                nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                activo = saleData[USUARIOS.ACTIVO] ?: true
            )
            val cliente = saleData[VENTAS.CLIENTE_ID]?.let {
                Cliente(
                    id = Id(it),
                    nombre = saleData[CLIENTES.RAZON_SOCIAL]!!,
                    cuit = saleData[CLIENTES.CUIT]
                )
            }

            val items = dsl.selectFrom(DETALLES_VENTA)
                .where(DETALLES_VENTA.VENTA_ID.eq(id.value))
                .fetch()
                .map { record ->
                    SaleItem.fromPersistence(
                        productoCodigo = record.productoCodigoRef!!,
                        cantidad = record.cantidad!!,
                        precioUnitario = record.precioUnitario!!,
                        tipoIvaId = record.tipoIvaId!!,
                        subtotal = record.subtotal!!,
                        baseImp = record.baseImp!!,
                        importeIva = record.importeIva!!
                    )
                }

            Sale.fromPersistence(
                id = Id(saleData[VENTAS.ID]!!),
                numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                cliente = cliente,
                usuario = usuario, // Cambio de vendedor a usuario
                fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                items = items
            )
        }
    }

    override fun saveComprobante(comprobante: Comprobante): Id {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)

            val numeroComprobante = ctx.select(DSL.coalesce(DSL.max(COMPROBANTES.NUMERO_COMPROBANTE), 0) + 1)
                .from(COMPROBANTES)
                .where(COMPROBANTES.PUNTO_VENTA.eq(comprobante.puntoVenta))
                .fetchOne(0, Int::class.java) ?: 1

            val record = ctx.newRecord(COMPROBANTES).apply {
                ventaId = comprobante.venta.value
                tipoComprobante = comprobante.tipoComprobante
                puntoVenta = comprobante.puntoVenta
                this.numeroComprobante = numeroComprobante
                cae = comprobante.cae
                fechaEmision = comprobante.fechaEmision
                fechaVencimientoCae = comprobante.fechaVencimientoCae
                impTotal = comprobante.impTotal
                impTotConc = comprobante.impTotConc
                impNeto = comprobante.impNeto
                impIva = comprobante.impIva
                impTrib = comprobante.impTrib
                monId = comprobante.monId
                monCotiz = comprobante.monCotiz
                estado = comprobante.estado
                creadoEn = LocalDateTime.now()
            }
            record.store()

            Id(record.id ?: throw IllegalStateException("Error al guardar el comprobante"))
        }
    }

    override fun findComprobanteById(id: Id): Comprobante? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(COMPROBANTES)
                .where(COMPROBANTES.ID.eq(id.value))
                .fetchOne()
                ?.let { record ->
                    Comprobante.fromPersistence(
                        id = Id(record.id!!),
                        venta = Id(record.ventaId!!),
                        tipoComprobante = record.tipoComprobante!!,
                        puntoVenta = record.puntoVenta!!,
                        numeroComprobante = record.numeroComprobante!!,
                        cae = record.cae!!,
                        fechaEmision = record.fechaEmision!!,
                        fechaVencimientoCae = record.fechaVencimientoCae!!,
                        impTotal = record.impTotal!!,
                        impTotConc = record.impTotConc!!,
                        impNeto = record.impNeto!!,
                        impIva = record.impIva!!,
                        impTrib = record.impTrib!!,
                        monId = record.monId!!,
                        monCotiz = record.monCotiz!!,
                        estado = record.estado!!
                    )
                }
        }
    }

    override fun updateComprobanteEmitido(ventaId: Id, emitido: Boolean): Boolean {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)
            val rowsUpdated = ctx.update(VENTAS)
                .set(VENTAS.COMPROBANTE_EMITIDO, emitido)
                .where(VENTAS.ID.eq(ventaId.value))
                .execute()
            rowsUpdated > 0
        }
    }

    override fun findSaleByNumeroVenta(numeroVenta: String): Sale? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val saleData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.CLIENTE_ID,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO,
                CLIENTES.RAZON_SOCIAL,
                CLIENTES.CUIT
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .leftJoin(CLIENTES).on(VENTAS.CLIENTE_ID.eq(CLIENTES.ID))
                .where(VENTAS.NUMERO_VENTA.eq(numeroVenta))
                .fetchOne() ?: return@transactionResult null

            val ventaId = Id(saleData[VENTAS.ID]!!)

            val usuario = Usuario(
                username = saleData[VENTAS.USUARIO_USERNAME]!!,
                nombre = saleData[USUARIOS.NOMBRE]!!,
                nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                activo = saleData[USUARIOS.ACTIVO] ?: true
            )
            val cliente = saleData[VENTAS.CLIENTE_ID]?.let {
                Cliente(
                    id = Id(it),
                    nombre = saleData[CLIENTES.RAZON_SOCIAL]!!,
                    cuit = saleData[CLIENTES.CUIT]
                )
            }

            val items = dsl.selectFrom(DETALLES_VENTA)
                .where(DETALLES_VENTA.VENTA_ID.eq(ventaId.value))
                .fetch()
                .map { record ->
                    SaleItem.fromPersistence(
                        productoCodigo = record.productoCodigoRef!!,
                        cantidad = record.cantidad!!,
                        precioUnitario = record.precioUnitario!!,
                        tipoIvaId = record.tipoIvaId!!,
                        subtotal = record.subtotal!!,
                        baseImp = record.baseImp!!,
                        importeIva = record.importeIva!!
                    )
                }

            Sale.fromPersistence(
                id = ventaId,
                numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                cliente = cliente,
                usuario = usuario,
                fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                items = items
            )
        }
    }

    override fun findSalesByDateRange(startDate: LocalDateTime, endDate: LocalDateTime): List<Sale> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val salesData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.CLIENTE_ID,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO,
                CLIENTES.RAZON_SOCIAL,
                CLIENTES.CUIT
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .leftJoin(CLIENTES).on(VENTAS.CLIENTE_ID.eq(CLIENTES.ID))
                .where(VENTAS.FECHA_VENTA.between(startDate, endDate))
                .orderBy(VENTAS.FECHA_VENTA.desc())
                .fetch()

            salesData.map { saleData ->
                val ventaId = Id(saleData[VENTAS.ID]!!)

                val usuario = Usuario(
                    username = saleData[VENTAS.USUARIO_USERNAME]!!,
                    nombre = saleData[USUARIOS.NOMBRE]!!,
                    nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                    activo = saleData[USUARIOS.ACTIVO] ?: true
                )
                val cliente = saleData[VENTAS.CLIENTE_ID]?.let {
                    Cliente(
                        id = Id(it),
                        nombre = saleData[CLIENTES.RAZON_SOCIAL]!!,
                        cuit = saleData[CLIENTES.CUIT]
                    )
                }

                val items = dsl.selectFrom(DETALLES_VENTA)
                    .where(DETALLES_VENTA.VENTA_ID.eq(ventaId.value))
                    .fetch()
                    .map { record ->
                        SaleItem.fromPersistence(
                            productoCodigo = record.productoCodigoRef!!,
                            cantidad = record.cantidad!!,
                            precioUnitario = record.precioUnitario!!,
                            tipoIvaId = record.tipoIvaId!!,
                            subtotal = record.subtotal!!,
                            baseImp = record.baseImp!!,
                            importeIva = record.importeIva!!
                        )
                    }

                Sale.fromPersistence(
                    id = ventaId,
                    numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                    cliente = cliente,
                    usuario = usuario,
                    fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                    montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                    comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                    medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                    codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                    idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                    items = items
                )
            }
        }
    }

    override fun findSalesByUsuario(username: String): List<Sale> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val salesData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.CLIENTE_ID,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO,
                CLIENTES.RAZON_SOCIAL,
                CLIENTES.CUIT
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .leftJoin(CLIENTES).on(VENTAS.CLIENTE_ID.eq(CLIENTES.ID))
                .where(VENTAS.USUARIO_USERNAME.eq(username))
                .orderBy(VENTAS.FECHA_VENTA.desc())
                .fetch()

            salesData.map { saleData ->
                val ventaId = Id(saleData[VENTAS.ID]!!)

                val usuario = Usuario(
                    username = saleData[VENTAS.USUARIO_USERNAME]!!,
                    nombre = saleData[USUARIOS.NOMBRE]!!,
                    nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                    activo = saleData[USUARIOS.ACTIVO] ?: true
                )
                val cliente = saleData[VENTAS.CLIENTE_ID]?.let {
                    Cliente(
                        id = Id(it),
                        nombre = saleData[CLIENTES.RAZON_SOCIAL]!!,
                        cuit = saleData[CLIENTES.CUIT]
                    )
                }

                val items = dsl.selectFrom(DETALLES_VENTA)
                    .where(DETALLES_VENTA.VENTA_ID.eq(ventaId.value))
                    .fetch()
                    .map { record ->
                        SaleItem.fromPersistence(
                            productoCodigo = record.productoCodigoRef!!,
                            cantidad = record.cantidad!!,
                            precioUnitario = record.precioUnitario!!,
                            tipoIvaId = record.tipoIvaId!!,
                            subtotal = record.subtotal!!,
                            baseImp = record.baseImp!!,
                            importeIva = record.importeIva!!
                        )
                    }

                Sale.fromPersistence(
                    id = ventaId,
                    numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                    cliente = cliente,
                    usuario = usuario,
                    fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                    montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                    comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                    medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                    codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                    idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                    items = items
                )
            }
        }
    }

    override fun findSalesByCliente(clienteId: Id): List<Sale> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val salesData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.CLIENTE_ID,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO,
                CLIENTES.RAZON_SOCIAL,
                CLIENTES.CUIT
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .leftJoin(CLIENTES).on(VENTAS.CLIENTE_ID.eq(CLIENTES.ID))
                .where(VENTAS.CLIENTE_ID.eq(clienteId.value))
                .orderBy(VENTAS.FECHA_VENTA.desc())
                .fetch()

            salesData.map { saleData ->
                val ventaId = Id(saleData[VENTAS.ID]!!)

                val usuario = Usuario(
                    username = saleData[VENTAS.USUARIO_USERNAME]!!,
                    nombre = saleData[USUARIOS.NOMBRE]!!,
                    nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                    activo = saleData[USUARIOS.ACTIVO] ?: true
                )
                val cliente = saleData[VENTAS.CLIENTE_ID]?.let {
                    Cliente(
                        id = Id(it),
                        nombre = saleData[CLIENTES.RAZON_SOCIAL]!!,
                        cuit = saleData[CLIENTES.CUIT]
                    )
                }

                val items = dsl.selectFrom(DETALLES_VENTA)
                    .where(DETALLES_VENTA.VENTA_ID.eq(ventaId.value))
                    .fetch()
                    .map { record ->
                        SaleItem.fromPersistence(
                            productoCodigo = record.productoCodigoRef!!,
                            cantidad = record.cantidad!!,
                            precioUnitario = record.precioUnitario!!,
                            tipoIvaId = record.tipoIvaId!!,
                            subtotal = record.subtotal!!,
                            baseImp = record.baseImp!!,
                            importeIva = record.importeIva!!
                        )
                    }

                Sale.fromPersistence(
                    id = ventaId,
                    numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                    cliente = cliente,
                    usuario = usuario,
                    fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                    montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                    comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                    medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                    codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                    idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                    items = items
                )
            }
        }
    }

    override fun findComprobantesByVentaId(ventaId: Id): List<Comprobante> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(COMPROBANTES)
                .where(COMPROBANTES.VENTA_ID.eq(ventaId.value))
                .orderBy(COMPROBANTES.FECHA_EMISION.desc())
                .fetch()
                .map { record ->
                    Comprobante.fromPersistence(
                        id = Id(record.id!!),
                        venta = Id(record.ventaId!!),
                        tipoComprobante = record.tipoComprobante!!,
                        puntoVenta = record.puntoVenta!!,
                        numeroComprobante = record.numeroComprobante!!,
                        cae = record.cae!!,
                        fechaEmision = record.fechaEmision!!,
                        fechaVencimientoCae = record.fechaVencimientoCae!!,
                        impTotal = record.impTotal!!,
                        impTotConc = record.impTotConc!!,
                        impNeto = record.impNeto!!,
                        impIva = record.impIva!!,
                        impTrib = record.impTrib!!,
                        monId = record.monId!!,
                        monCotiz = record.monCotiz!!,
                        estado = record.estado!!
                    )
                }
        }
    }

    override fun findComprobanteByNumero(puntoVenta: Int, numeroComprobante: Int): Comprobante? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(COMPROBANTES)
                .where(COMPROBANTES.PUNTO_VENTA.eq(puntoVenta))
                .and(COMPROBANTES.NUMERO_COMPROBANTE.eq(numeroComprobante))
                .fetchOne()
                ?.let { record ->
                    Comprobante.fromPersistence(
                        id = Id(record.id!!),
                        venta = Id(record.ventaId!!),
                        tipoComprobante = record.tipoComprobante!!,
                        puntoVenta = record.puntoVenta!!,
                        numeroComprobante = record.numeroComprobante!!,
                        cae = record.cae!!,
                        fechaEmision = record.fechaEmision!!,
                        fechaVencimientoCae = record.fechaVencimientoCae!!,
                        impTotal = record.impTotal!!,
                        impTotConc = record.impTotConc!!,
                        impNeto = record.impNeto!!,
                        impIva = record.impIva!!,
                        impTrib = record.impTrib!!,
                        monId = record.monId!!,
                        monCotiz = record.monCotiz!!,
                        estado = record.estado!!
                    )
                }
        }
    }

    override fun countSalesWithoutComprobante(): Int {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectCount()
                .from(VENTAS)
                .where(VENTAS.COMPROBANTE_EMITIDO.eq(false))
                .fetchOne(0, Int::class.java) ?: 0
        }
    }

    override fun findSalesWithoutComprobante(limit: Int): List<Sale> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val salesData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.CLIENTE_ID,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO,
                CLIENTES.RAZON_SOCIAL,
                CLIENTES.CUIT
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .leftJoin(CLIENTES).on(VENTAS.CLIENTE_ID.eq(CLIENTES.ID))
                .where(VENTAS.COMPROBANTE_EMITIDO.eq(false))
                .orderBy(VENTAS.FECHA_VENTA.desc())
                .limit(limit)
                .fetch()

            salesData.map { saleData ->
                val ventaId = Id(saleData[VENTAS.ID]!!)

                val usuario = Usuario(
                    username = saleData[VENTAS.USUARIO_USERNAME]!!,
                    nombre = saleData[USUARIOS.NOMBRE]!!,
                    nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                    activo = saleData[USUARIOS.ACTIVO] ?: true
                )
                val cliente = saleData[VENTAS.CLIENTE_ID]?.let {
                    Cliente(
                        id = Id(it),
                        nombre = saleData[CLIENTES.RAZON_SOCIAL]!!,
                        cuit = saleData[CLIENTES.CUIT]
                    )
                }

                val items = dsl.selectFrom(DETALLES_VENTA)
                    .where(DETALLES_VENTA.VENTA_ID.eq(ventaId.value))
                    .fetch()
                    .map { record ->
                        SaleItem.fromPersistence(
                            productoCodigo = record.productoCodigoRef!!,
                            cantidad = record.cantidad!!,
                            precioUnitario = record.precioUnitario!!,
                            tipoIvaId = record.tipoIvaId!!,
                            subtotal = record.subtotal!!,
                            baseImp = record.baseImp!!,
                            importeIva = record.importeIva!!
                        )
                    }

                Sale.fromPersistence(
                    id = ventaId,
                    numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                    cliente = cliente,
                    usuario = usuario,
                    fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                    montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                    comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                    medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                    codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                    idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                    items = items
                )
            }
        }
    }
}