package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.application.port.`in`.PrintService
import com.gnico.majo.adapter.controller.dto.VentaParaReimpresionInfoResponse
import com.gnico.majo.adapter.controller.dto.ComprobanteParaReimpresionInfoResponse
import com.gnico.majo.adapter.controller.dto.toResponse
import com.gnico.majo.application.domain.model.Id
import kotlinx.serialization.Serializable

class PrintController(
    private val printService: PrintService
) {
    
    /**
     * Imprime un ticket genérico para una venta (sin comprobante fiscal)
     */
    fun imprimirTicketVenta(ventaId: Int): PrintResponse {
        return try {
            printService.imprimirTicketVenta(Id(ventaId))
            PrintResponse(
                success = true,
                message = "Ticket de venta impreso exitosamente"
            )
        } catch (e: Exception) {
            PrintResponse(
                success = false,
                message = "Error al imprimir ticket de venta",
                error = e.message
            )
        }
    }
    
    /**
     * Imprime un ticket de comprobante fiscal
     */
    fun imprimirTicketComprobante(comprobanteId: Int): PrintResponse {
        return try {
            printService.imprimirTicketComprobante(Id(comprobanteId))
            PrintResponse(
                success = true,
                message = "Ticket de comprobante impreso exitosamente"
            )
        } catch (e: Exception) {
            PrintResponse(
                success = false,
                message = "Error al imprimir ticket de comprobante",
                error = e.message
            )
        }
    }
    
    /**
     * Imprime un comprobante fiscal completo (formato oficial)
     */
    fun imprimirComprobanteFiscal(comprobanteId: Int): PrintResponse {
        return try {
            printService.imprimirComprobanteFiscal(Id(comprobanteId))
            PrintResponse(
                success = true,
                message = "Comprobante fiscal impreso exitosamente"
            )
        } catch (e: Exception) {
            PrintResponse(
                success = false,
                message = "Error al imprimir comprobante fiscal",
                error = e.message
            )
        }
    }
    
    /**
     * Busca ventas por diferentes criterios para reimpresión
     */
    fun buscarVentasParaReimpresion(request: BuscarVentasRequest): List<VentaParaReimpresionInfoResponse> {
        return printService.buscarVentasParaReimpresion(
            numeroVenta = request.numeroVenta,
            fechaDesde = request.fechaDesde,
            fechaHasta = request.fechaHasta,
            usuario = request.usuario
        ).map { it.toResponse() }
    }

    /**
     * Busca comprobantes por diferentes criterios para reimpresión
     */
    fun buscarComprobantesParaReimpresion(request: BuscarComprobantesRequest): List<ComprobanteParaReimpresionInfoResponse> {
        return printService.buscarComprobantesParaReimpresion(
            puntoVenta = request.puntoVenta,
            numeroComprobante = request.numeroComprobante,
            fechaDesde = request.fechaDesde,
            fechaHasta = request.fechaHasta
        ).map { it.toResponse() }
    }
}

@Serializable
data class PrintResponse(
    val success: Boolean,
    val message: String,
    val error: String? = null
)

@Serializable
data class BuscarVentasRequest(
    val numeroVenta: String? = null,
    val fechaDesde: String? = null,
    val fechaHasta: String? = null,
    val usuario: String? = null
)

@Serializable
data class BuscarComprobantesRequest(
    val puntoVenta: Int? = null,
    val numeroComprobante: Int? = null,
    val fechaDesde: String? = null,
    val fechaHasta: String? = null
)
