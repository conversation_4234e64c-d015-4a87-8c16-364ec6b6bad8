package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.adapter.controller.dto.ProductoCodigoResponse
import com.gnico.majo.adapter.controller.dto.ProductoDto
import com.gnico.majo.adapter.controller.dto.ProductoListResponse
import com.gnico.majo.adapter.controller.dto.ProductoResponse
import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto
import com.gnico.majo.application.domain.model.TipoIva
import java.math.BigDecimal

class ProductoController(private val productoService: ProductoService) {

    fun getAllProductos(): ProductoListResponse {
        val productos = productoService.getAllProductos()
        return ProductoListResponse(
            productos = productos.map { mapToProductoResponse(it) }
        )
    }

    fun getProductoByCodigo(codigo: Int): ProductoResponse? {
        val producto = productoService.getProductoByCodigo(codigo)
        return producto?.let { mapToProductoResponse(it) }
    }

    fun createProducto(request: ProductoDto): ProductoCodigoResponse {
        val producto = mapToProducto(request)
        val codigo = productoService.createProducto(producto)
        return ProductoCodigoResponse(codigo)
    }

    fun updateProducto(request: ProductoDto): Boolean {
        val producto = mapToProducto(request)
        return productoService.updateProducto(producto)
    }

    fun deleteProducto(codigo: Int): Boolean {
        return productoService.deleteProducto(codigo)
    }

    private fun mapToProducto(request: ProductoDto): Producto {
        return Producto.create(
            codigo = request.codigo,
            nombre = request.nombre,
            descripcion = request.descripcion,
            unidadMedida = Id(request.unidadMedidaId),
            tipoIva = TipoIva.fromIdOrThrow(request.tipoIvaId),
            categoria = request.categoriaId?.let { Id(it) },
            precioUnitario = request.precioUnitario?.let { BigDecimal(it.toString()) },
            stockActual = request.stockActual,
            activo = request.activo
        )
    }

    private fun mapToProductoResponse(producto: Producto): ProductoResponse {
        return ProductoResponse(
            codigo = producto.codigo,
            nombre = producto.nombre,
            descripcion = producto.descripcion,
            unidadMedidaId = producto.unidadMedida.value,
            tipoIvaId = producto.tipoIva.id,
            categoriaId = producto.categoria?.value,
            precioUnitario = producto.precioUnitario?.toDouble(),
            stockActual = producto.stockActual,
            activo = producto.activo
        )
    }
}