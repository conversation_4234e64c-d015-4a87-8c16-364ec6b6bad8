package com.gnico.majo.adapter.controller.dto

import com.gnico.majo.application.port.`in`.ComprobanteInfo
import com.gnico.majo.application.port.`in`.VentaSinComprobanteInfo
import kotlinx.serialization.Serializable

/**
 * DTOs para serialización de respuestas de comprobantes
 */

@Serializable
data class ComprobanteInfoResponse(
    val id: Int,
    val ventaId: Int,
    val tipoComprobante: String,
    val puntoVenta: Int,
    val numeroComprobante: Int,
    val cae: String,
    val estado: String,
    val fechaEmision: String,
    val montoTotal: String
)

@Serializable
data class VentaSinComprobanteInfoResponse(
    val ventaId: Int,
    val numeroVenta: String,
    val fechaVenta: String,
    val clienteNombre: String?,
    val usuarioNombre: String,
    val montoTotal: String,
    val medioPago: String
)

// Funciones de extensión para convertir entre DTOs
fun ComprobanteInfo.toResponse(): ComprobanteInfoResponse {
    return ComprobanteInfoResponse(
        id = this.id.value,
        ventaId = this.ventaId.value,
        tipoComprobante = this.tipoComprobante,
        puntoVenta = this.puntoVenta,
        numeroComprobante = this.numeroComprobante,
        cae = this.cae,
        estado = this.estado,
        fechaEmision = this.fechaEmision,
        montoTotal = this.montoTotal
    )
}

fun VentaSinComprobanteInfo.toResponse(): VentaSinComprobanteInfoResponse {
    return VentaSinComprobanteInfoResponse(
        ventaId = this.ventaId.value,
        numeroVenta = this.numeroVenta,
        fechaVenta = this.fechaVenta,
        clienteNombre = this.clienteNombre,
        usuarioNombre = this.usuarioNombre,
        montoTotal = this.montoTotal,
        medioPago = this.medioPago
    )
}
