package com.gnico.majo.adapter.controller.dto

import com.gnico.majo.application.port.`in`.VentaParaReimpresionInfo
import com.gnico.majo.application.port.`in`.ComprobanteParaReimpresionInfo
import com.gnico.majo.application.port.`in`.ComprobanteBasicoInfo
import kotlinx.serialization.Serializable

/**
 * DTOs para serialización de respuestas de impresión
 */

@Serializable
data class VentaParaReimpresionInfoResponse(
    val ventaId: Int,
    val numeroVenta: String,
    val fechaVenta: String,
    val clienteNombre: String?,
    val usuarioNombre: String,
    val montoTotal: String,
    val medioPago: String,
    val tieneComprobante: Boolean,
    val comprobantes: List<ComprobanteBasicoInfoResponse>
)

@Serializable
data class ComprobanteBasicoInfoResponse(
    val comprobanteId: Int,
    val tipoComprobante: String,
    val puntoVenta: Int,
    val numeroComprobante: Int,
    val estado: String
)

@Serializable
data class ComprobanteParaReimpresionInfoResponse(
    val comprobanteId: Int,
    val ventaId: Int,
    val numeroVenta: String,
    val tipoComprobante: String,
    val puntoVenta: Int,
    val numeroComprobante: Int,
    val cae: String,
    val fechaEmision: String,
    val clienteNombre: String?,
    val usuarioNombre: String,
    val montoTotal: String,
    val estado: String
)

// Funciones de extensión para convertir entre DTOs
fun VentaParaReimpresionInfo.toResponse(): VentaParaReimpresionInfoResponse {
    return VentaParaReimpresionInfoResponse(
        ventaId = this.ventaId.value,
        numeroVenta = this.numeroVenta,
        fechaVenta = this.fechaVenta,
        clienteNombre = this.clienteNombre,
        usuarioNombre = this.usuarioNombre,
        montoTotal = this.montoTotal,
        medioPago = this.medioPago,
        tieneComprobante = this.tieneComprobante,
        comprobantes = this.comprobantes.map { it.toResponse() }
    )
}

fun ComprobanteBasicoInfo.toResponse(): ComprobanteBasicoInfoResponse {
    return ComprobanteBasicoInfoResponse(
        comprobanteId = this.comprobanteId.value,
        tipoComprobante = this.tipoComprobante,
        puntoVenta = this.puntoVenta,
        numeroComprobante = this.numeroComprobante,
        estado = this.estado
    )
}

fun ComprobanteParaReimpresionInfo.toResponse(): ComprobanteParaReimpresionInfoResponse {
    return ComprobanteParaReimpresionInfoResponse(
        comprobanteId = this.comprobanteId.value,
        ventaId = this.ventaId.value,
        numeroVenta = this.numeroVenta,
        tipoComprobante = this.tipoComprobante,
        puntoVenta = this.puntoVenta,
        numeroComprobante = this.numeroComprobante,
        cae = this.cae,
        fechaEmision = this.fechaEmision,
        clienteNombre = this.clienteNombre,
        usuarioNombre = this.usuarioNombre,
        montoTotal = this.montoTotal,
        estado = this.estado
    )
}
