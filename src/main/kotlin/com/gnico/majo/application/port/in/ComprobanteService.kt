package com.gnico.majo.application.port.`in`

import com.gnico.majo.application.domain.model.Id

/**
 * Servicio especializado para manejo de comprobantes fiscales
 * Permite generar comprobantes de forma desfasada sobre ventas ya realizadas
 */
interface ComprobanteService {
    
    /**
     * Genera un comprobante fiscal online (CAE) para una venta existente
     * @param ventaId ID de la venta para la cual generar el comprobante
     * @param tipoComprobante Tipo de comprobante (opcional, default: FACTURA_B desde .env)
     * @param puntoVenta Punto de venta a utilizar (opcional, default: PUNTO_DE_VENTA desde .env)
     * @return ID del comprobante generado
     * @throws IllegalArgumentException si la venta no existe o ya tiene comprobante
     * @throws IllegalStateException si hay error en la comunicación con AFIP
     */
    suspend fun generarComprobanteOnline(
        ventaId: Id,
        tipoComprobante: String? = null,
        puntoVenta: Int? = null
    ): Id
    
    /**
     * Genera un comprobante fiscal offline (CAEA) para una venta existente
     * @param ventaId ID de la venta para la cual generar el comprobante
     * @param tipoComprobante Tipo de comprobante (opcional, default: FACTURA_B desde .env)
     * @param puntoVenta Punto de venta a utilizar (opcional, default: PUNTO_DE_VENTA desde .env)
     * @return ID del comprobante generado
     * @throws IllegalArgumentException si la venta no existe o ya tiene comprobante
     * @throws IllegalStateException si hay error en la generación del comprobante
     */
    suspend fun generarComprobanteOffline(
        ventaId: Id,
        tipoComprobante: String? = null,
        puntoVenta: Int? = null
    ): Id
    
    /**
     * Busca comprobantes por ID de venta
     * @param ventaId ID de la venta
     * @return Lista de comprobantes asociados a la venta
     */
    fun buscarComprobantesPorVenta(ventaId: Id): List<ComprobanteInfo>
    
    /**
     * Busca un comprobante por su número
     * @param puntoVenta Punto de venta
     * @param numeroComprobante Número del comprobante
     * @return Información del comprobante o null si no existe
     */
    fun buscarComprobantePorNumero(puntoVenta: Int, numeroComprobante: Int): ComprobanteInfo?
    
    /**
     * Obtiene estadísticas de ventas sin comprobante
     * @return Número de ventas que no tienen comprobante emitido
     */
    fun contarVentasSinComprobante(): Int
    
    /**
     * Obtiene lista de ventas sin comprobante para procesamiento
     * @param limit Límite de resultados (default: 100)
     * @return Lista de información de ventas sin comprobante
     */
    fun obtenerVentasSinComprobante(limit: Int = 100): List<VentaSinComprobanteInfo>
}

/**
 * DTO con información básica de un comprobante
 */
data class ComprobanteInfo(
    val id: Id,
    val ventaId: Id,
    val tipoComprobante: String,
    val puntoVenta: Int,
    val numeroComprobante: Int,
    val cae: String,
    val estado: String,
    val fechaEmision: String,
    val montoTotal: String
)

/**
 * DTO con información de ventas que no tienen comprobante
 */
data class VentaSinComprobanteInfo(
    val ventaId: Id,
    val numeroVenta: String,
    val fechaVenta: String,
    val clienteNombre: String?,
    val usuarioNombre: String,
    val montoTotal: String,
    val medioPago: String
)
