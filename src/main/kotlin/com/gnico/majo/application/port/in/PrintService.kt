package com.gnico.majo.application.port.`in`

import com.gnico.majo.application.domain.model.Id

/**
 * Servicio especializado para impresión de tickets y comprobantes
 * Permite reimprimir documentos ya procesados
 */
interface PrintService {
    
    /**
     * Imprime un ticket genérico para una venta (sin comprobante fiscal)
     * @param ventaId ID de la venta a imprimir
     * @throws IllegalArgumentException si la venta no existe
     */
    fun imprimirTicketVenta(ventaId: Id)
    
    /**
     * Imprime un ticket de comprobante fiscal
     * @param comprobanteId ID del comprobante a imprimir
     * @throws IllegalArgumentException si el comprobante no existe
     */
    fun imprimirTicketComprobante(comprobanteId: Id)
    
    /**
     * Imprime un comprobante fiscal completo (formato oficial)
     * @param comprobanteId ID del comprobante a imprimir
     * @throws IllegalArgumentException si el comprobante no existe
     */
    fun imprimirComprobanteFiscal(comprobanteId: Id)
    
    /**
     * Busca ventas por diferentes criterios para reimpresión
     * @param numeroVenta Número de venta (opcional)
     * @param fechaDesde Fecha desde (opcional)
     * @param fechaHasta Fecha hasta (opcional)
     * @param usuario Username del usuario (opcional)
     * @return Lista de ventas que coinciden con los criterios
     */
    fun buscarVentasParaReimpresion(
        numeroVenta: String? = null,
        fechaDesde: String? = null,
        fechaHasta: String? = null,
        usuario: String? = null
    ): List<VentaParaReimpresionInfo>
    
    /**
     * Busca comprobantes por diferentes criterios para reimpresión
     * @param puntoVenta Punto de venta (opcional)
     * @param numeroComprobante Número de comprobante (opcional)
     * @param fechaDesde Fecha desde (opcional)
     * @param fechaHasta Fecha hasta (opcional)
     * @return Lista de comprobantes que coinciden con los criterios
     */
    fun buscarComprobantesParaReimpresion(
        puntoVenta: Int? = null,
        numeroComprobante: Int? = null,
        fechaDesde: String? = null,
        fechaHasta: String? = null
    ): List<ComprobanteParaReimpresionInfo>
}

/**
 * DTO con información de venta para reimpresión
 */
data class VentaParaReimpresionInfo(
    val ventaId: Id,
    val numeroVenta: String,
    val fechaVenta: String,
    val clienteNombre: String?,
    val usuarioNombre: String,
    val montoTotal: String,
    val medioPago: String,
    val tieneComprobante: Boolean,
    val comprobantes: List<ComprobanteBasicoInfo>
)

/**
 * DTO con información básica de comprobante
 */
data class ComprobanteBasicoInfo(
    val comprobanteId: Id,
    val tipoComprobante: String,
    val puntoVenta: Int,
    val numeroComprobante: Int,
    val estado: String
)

/**
 * DTO con información de comprobante para reimpresión
 */
data class ComprobanteParaReimpresionInfo(
    val comprobanteId: Id,
    val ventaId: Id,
    val numeroVenta: String,
    val tipoComprobante: String,
    val puntoVenta: Int,
    val numeroComprobante: Int,
    val cae: String,
    val fechaEmision: String,
    val clienteNombre: String?,
    val usuarioNombre: String,
    val montoTotal: String,
    val estado: String
)
