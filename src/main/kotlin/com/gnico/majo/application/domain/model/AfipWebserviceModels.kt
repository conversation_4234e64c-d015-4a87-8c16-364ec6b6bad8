package com.gnico.majo.application.domain.model

import java.math.BigDecimal
import java.time.LocalDate

/**
 * Modelos de dominio para los webservices de AFIP
 * Estos modelos encapsulan la lógica de negocio relacionada con AFIP
 */

/**
 * Credenciales de autenticación para AFIP
 */
data class AfipCredentials(
    val token: String,
    val sign: String,
    val cuit: Long
) {
    fun isValid(): Boolean = token.isNotBlank() && sign.isNotBlank() && cuit > 0
    
    fun isExpired(): Boolean {
        // TODO: Implementar lógica de verificación de expiración del token
        // Por ahora asumimos que es válido
        return false
    }
}

/**
 * Configuración para solicitud de CAE
 */
data class AfipCAERequest(
    val sale: Sale,
    val tipoComprobante: TipoComprobanteAfip,
    val puntoVenta: Int,
    val credentials: AfipCredentials
) {
    init {
        require(puntoVenta > 0) { "El punto de venta debe ser positivo" }
        require(credentials.isValid()) { "Las credenciales deben ser válidas" }
    }
    
    /**
     * Convierte la venta a los datos requeridos por AFIP
     */
    fun toAfipInvoiceData(): AfipInvoiceData {
        val taxAmounts = sale.calculateTaxAmounts()
        
        return AfipInvoiceData(
            concepto = 1, // Productos
            docTipo = 80, // CUIT por defecto
            docNro = sale.cliente?.cuit?.toLongOrNull() ?: 0L,
            impTotal = taxAmounts.impTotal,
            impTotConc = taxAmounts.impTotConc,
            impNeto = taxAmounts.impNeto,
            impIva = taxAmounts.impIva,
            impTrib = taxAmounts.impTrib,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            ivaDetails = sale.getIvaDetails()
        )
    }
}

/**
 * Datos de factura para enviar a AFIP
 */
data class AfipInvoiceData(
    val concepto: Int,
    val docTipo: Int,
    val docNro: Long,
    val impTotal: BigDecimal,
    val impTotConc: BigDecimal,
    val impNeto: BigDecimal,
    val impIva: BigDecimal,
    val impTrib: BigDecimal,
    val monId: String,
    val monCotiz: BigDecimal,
    val ivaDetails: List<IvaDetail>
)

/**
 * Detalle de IVA para AFIP
 */
data class IvaDetail(
    val id: Int, // Código de alícuota IVA en AFIP
    val baseImponible: BigDecimal,
    val importe: BigDecimal
)

/**
 * Tipos de comprobante AFIP
 */
enum class TipoComprobanteAfip(val codigo: Int, val descripcion: String) {
    FACTURA_A(1, "Factura A"),
    FACTURA_B(6, "Factura B"),
    FACTURA_C(11, "Factura C"),
    NOTA_CREDITO_A(3, "Nota de Crédito A"),
    NOTA_CREDITO_B(8, "Nota de Crédito B"),
    NOTA_CREDITO_C(13, "Nota de Crédito C"),
    NOTA_DEBITO_A(2, "Nota de Débito A"),
    NOTA_DEBITO_B(7, "Nota de Débito B"),
    NOTA_DEBITO_C(12, "Nota de Débito C");
    
    companion object {
        fun fromString(tipo: String): TipoComprobanteAfip? = when (tipo.uppercase()) {
            "FACTURA_A" -> FACTURA_A
            "FACTURA_B" -> FACTURA_B
            "FACTURA_C" -> FACTURA_C
            "NOTA_CREDITO_A" -> NOTA_CREDITO_A
            "NOTA_CREDITO_B" -> NOTA_CREDITO_B
            "NOTA_CREDITO_C" -> NOTA_CREDITO_C
            "NOTA_DEBITO_A" -> NOTA_DEBITO_A
            "NOTA_DEBITO_B" -> NOTA_DEBITO_B
            "NOTA_DEBITO_C" -> NOTA_DEBITO_C
            else -> null
        }
        
        fun fromCodigo(codigo: Int): TipoComprobanteAfip? = 
            values().find { it.codigo == codigo }
    }
}

/**
 * Respuesta cruda del webservice WSFE
 */
data class WsfeResponse(
    val cae: String,
    val caeFchVto: String,
    val resultado: String,
    val numeroComprobante: Long,
    val observaciones: List<String> = emptyList()
) {
    /**
     * Convierte la respuesta del webservice al modelo de dominio
     */
    fun toAfipResponse(): AfipResponse {
        return if (resultado == "A") {
            AfipResponse.createApprovedCAE(
                cae = cae,
                fechaVencimiento = parseFechaVencimiento(caeFchVto),
                numeroComprobante = numeroComprobante,
                observaciones = observaciones
            )
        } else {
            AfipResponse.createRejected(
                observaciones = if (observaciones.isEmpty()) 
                    listOf("Comprobante rechazado por AFIP") 
                else observaciones
            )
        }
    }
    
    private fun parseFechaVencimiento(fechaStr: String): LocalDate {
        return try {
            LocalDate.parse(fechaStr, java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"))
        } catch (e: Exception) {
            LocalDate.now().plusDays(10)
        }
    }
}

/**
 * Configuración de AFIP
 */
data class AfipConfiguration(
    val cuit: Long,
    val certificatePath: String,
    val certificatePassword: String,
    val certificateAlias: String = "1",
    val isProduction: Boolean = false
) {
    fun getWsaaUrl(): String = if (isProduction) {
        "https://wsaa.afip.gov.ar/ws/services/LoginCms"
    } else {
        "https://wsaahomo.afip.gov.ar/ws/services/LoginCms"
    }
    
    fun getWsfeUrl(): String = if (isProduction) {
        "https://servicios1.afip.gov.ar/wsfev1/service.asmx"
    } else {
        "https://wswhomo.afip.gov.ar/wsfev1/service.asmx"
    }
}
