package com.gnico.majo.application.domain.model

import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class Id(val value: Int) // Identificador genérico para el dominio

data class Cliente(val id: Id, val nombre: String, val cuit: String?)

@ConsistentCopyVisibility
data class Sale private constructor(
    val id: Id? = null,
    val numeroVenta: String,
    val cliente: Cliente?,
    val usuario: Usuario,
    val fechaVenta: LocalDateTime,
    val montoTotal: BigDecimal,
    val comprobanteEmitido: Boolean,
    val medioPago: String,
    val codigoTicketBalanza: String? = null,
    val idTicketBalanza: String? = null,
    val items: List<SaleItem>
) {
    companion object {
        fun create(
            cliente: Cliente?,
            usuario: Usuario,
            items: List<SaleItem>,
            medioPago: String,
            codigoTicketBalanza: String? = null,
            idTicketBalanza: String? = null
        ): Sale {
            require(items.isNotEmpty()) { "Una venta debe tener al menos un item" }
            require(medioPago.isNotBlank()) { "El medio de pago no puede estar vacío" }

            // Validar que el medio de pago sea válido
            MedioPago.fromString(medioPago)
                ?: throw IllegalArgumentException("Medio de pago '$medioPago' no válido")

            val numeroVenta = generateSaleNumber()
            val montoTotal = calculateTotal(items)

            return Sale(
                numeroVenta = numeroVenta,
                cliente = cliente,
                usuario = usuario,
                fechaVenta = LocalDateTime.now(),
                montoTotal = montoTotal,
                comprobanteEmitido = false,
                medioPago = medioPago,
                codigoTicketBalanza = codigoTicketBalanza,
                idTicketBalanza = idTicketBalanza,
                items = items
            )
        }

        private fun generateSaleNumber(): String {
            return "V-${UUID.randomUUID().toString().substring(0, 8)}"
        }

        private fun calculateTotal(items: List<SaleItem>): BigDecimal {
            return items.sumOf { it.totalWithTax() }
        }

        // Factory method para crear desde datos persistidos (usado por repositorios)
        fun fromPersistence(
            id: Id?,
            numeroVenta: String,
            cliente: Cliente?,
            usuario: Usuario,
            fechaVenta: LocalDateTime,
            montoTotal: BigDecimal,
            comprobanteEmitido: Boolean,
            medioPago: String,
            codigoTicketBalanza: String?,
            idTicketBalanza: String?,
            items: List<SaleItem>
        ): Sale {
            return Sale(
                id = id,
                numeroVenta = numeroVenta,
                cliente = cliente,
                usuario = usuario,
                fechaVenta = fechaVenta,
                montoTotal = montoTotal,
                comprobanteEmitido = comprobanteEmitido,
                medioPago = medioPago,
                codigoTicketBalanza = codigoTicketBalanza,
                idTicketBalanza = idTicketBalanza,
                items = items
            )
        }
    }

    fun canCreateComprobante(tipoComprobante: String): Boolean {
        val tipo = TipoComprobante.fromString(tipoComprobante) ?: return false
        return if (tipo.requiereCliente) cliente != null else true
    }

    fun validateComprobanteCreation(tipoComprobante: String) {
        val tipo = TipoComprobante.fromString(tipoComprobante)
            ?: throw IllegalArgumentException("Tipo de comprobante '$tipoComprobante' no válido")

        if (tipo.requiereCliente && cliente == null) {
            throw IllegalArgumentException("${tipo.descripcion} requiere un cliente")
        }
    }

    fun calculateTaxAmounts(): TaxAmounts {
        val impNeto = items.sumOf { it.baseImp }
        val impIva = items.sumOf { it.importeIva }
        val impTotal = impNeto.add(impIva)

        return TaxAmounts(
            impNeto = impNeto,
            impIva = impIva,
            impTotal = impTotal,
            impTotConc = BigDecimal.ZERO,
            impTrib = BigDecimal.ZERO
        )
    }

    /**
     * Obtiene los detalles de IVA agrupados por tipo para AFIP
     */
    fun getIvaDetails(): List<IvaDetail> {
        return items.groupBy { it.tipoIva }
            .map { (tipoIva, itemsGroup) ->
                val baseImponible = itemsGroup.sumOf { it.baseImp }
                val importeIva = itemsGroup.sumOf { it.importeIva }

                IvaDetail(
                    id = TipoIva.mapToAfipCode(tipoIva),
                    baseImponible = baseImponible,
                    importe = importeIva
                )
            }
            .filter { it.baseImponible > BigDecimal.ZERO }
    }
}

@ConsistentCopyVisibility
data class SaleItem private constructor(
    val productoCodigo: Int,
    val cantidad: BigDecimal,
    val precioUnitario: BigDecimal,
    val tipoIva: TipoIva,
    val subtotal: BigDecimal,
    val baseImp: BigDecimal,
    val importeIva: BigDecimal
) {
    companion object {
        /**
         * Crea un SaleItem donde el precio unitario incluye IVA
         */
        fun create(
            productoCodigo: Int,
            cantidad: BigDecimal,
            precioUnitario: BigDecimal,
            tipoIva: TipoIva
        ): SaleItem {
            require(cantidad > BigDecimal.ZERO) { "La cantidad debe ser mayor a cero" }
            require(precioUnitario >= BigDecimal.ZERO) { "El precio unitario no puede ser negativo" }

            val subtotal = cantidad.multiply(precioUnitario)

            // Calcular base imponible (precio sin IVA)
            val divisor = BigDecimal.ONE.add(tipoIva.getPorcentajeDecimal())
            val baseImp = subtotal.divide(divisor, 2, java.math.RoundingMode.HALF_UP)
            val importeIva = subtotal.subtract(baseImp)

            return SaleItem(
                productoCodigo = productoCodigo,
                cantidad = cantidad,
                precioUnitario = precioUnitario,
                tipoIva = tipoIva,
                subtotal = subtotal,
                baseImp = baseImp,
                importeIva = importeIva
            )
        }

        // Factory method para crear desde datos persistidos (usado por repositorios)
        fun fromPersistence(
            productoCodigo: Int,
            cantidad: BigDecimal,
            precioUnitario: BigDecimal,
            tipoIvaId: Int,
            subtotal: BigDecimal,
            baseImp: BigDecimal,
            importeIva: BigDecimal
        ): SaleItem {
            val tipoIva = TipoIva.fromIdOrThrow(tipoIvaId)
            return SaleItem(
                productoCodigo = productoCodigo,
                cantidad = cantidad,
                precioUnitario = precioUnitario,
                tipoIva = tipoIva,
                subtotal = subtotal,
                baseImp = baseImp,
                importeIva = importeIva
            )
        }
    }

    fun totalWithTax(): BigDecimal = subtotal
}