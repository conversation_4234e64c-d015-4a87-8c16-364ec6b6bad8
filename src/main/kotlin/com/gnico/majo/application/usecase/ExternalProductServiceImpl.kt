package com.gnico.majo.application.usecase

import com.gnico.majo.application.domain.model.ExternalProduct
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto
import com.gnico.majo.application.domain.model.TipoIva
import com.gnico.majo.application.port.`in`.ExternalProductService
import com.gnico.majo.application.port.out.ExternalProductRepositoryPort
import com.gnico.majo.application.port.out.ProductoRepositoryPort
import java.math.BigDecimal

class ExternalProductServiceImpl(
    private val externalProductRepository: ExternalProductRepositoryPort,
    private val productoRepository: ProductoRepositoryPort
) : ExternalProductService {

    override suspend fun getAllExternalProducts(): List<ExternalProduct> {
        return externalProductRepository.findAllProducts()
    }

    override suspend fun getExternalProductById(productId: Int): ExternalProduct? {
        require(productId >= 0) { "Product ID debe ser mayor o igual a 0" }
        return externalProductRepository.findProductById(productId)
    }

    override suspend fun syncExternalProducts(): Int {
        // 1. Limpiar la tabla interna de productos
        productoRepository.deleteAll()

        // 2. Obtener todos los productos externos
        val externalProducts = externalProductRepository.findAllProducts()

        // 3. Mapear productos externos a productos internos
        val productosInternos = externalProducts.map { externalProduct ->
            mapExternalToInternal(externalProduct)
        }

        // 4. Insertar productos en la tabla interna
        val syncedCount = productoRepository.saveAll(productosInternos)

        return syncedCount
    }

    /**
     * Mapea un producto externo a un producto interno
     */
    private fun mapExternalToInternal(externalProduct: ExternalProduct): Producto {
        return Producto.create(
            codigo = externalProduct.productId,
            nombre = externalProduct.name,
            descripcion = externalProduct.description,
            unidadMedida = Id(externalProduct.uomId),
            tipoIva = TipoIva.IVA_21, // Valor fijo como se especificó (ID 5 = 21%)
            categoria = null, // No se mapea desde productos externos
            precioUnitario = BigDecimal(externalProduct.price),
            stockActual = null, // Valor por defecto null
            activo = externalProduct.isActive
        )
    }
}
