package com.gnico.majo.application.usecase

import com.gnico.majo.application.port.`in`.ComprobanteService
import com.gnico.majo.application.port.`in`.ComprobanteInfo
import com.gnico.majo.application.port.`in`.VentaSinComprobanteInfo
import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.domain.model.AfipResponse
import com.gnico.majo.infrastructure.config.ComprobanteConfigurationService
import java.time.format.DateTimeFormatter
import java.text.DecimalFormat

class ComprobanteServiceImpl(
    private val saleRepository: SaleRepositoryPort,
    private val afipService: AfipService
) : ComprobanteService {
    
    private val dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
    private val decimalFormatter = DecimalFormat("#,##0.00")
    
    override suspend fun generarComprobanteOnline(
        ventaId: Id,
        tipoComprobante: String?,
        puntoVenta: Int?
    ): Id {
        // Resolver configuración con valores por defecto
        val config = ComprobanteConfigurationService.resolveConfig(
            puntoVenta = puntoVenta,
            tipoComprobante = tipoComprobante
        )

        // Validar que la venta existe
        val sale = saleRepository.findSaleById(ventaId)
            ?: throw IllegalArgumentException("Venta ${ventaId.value} no encontrada")

        // Validar que la venta no tiene comprobante ya emitido
        if (sale.comprobanteEmitido) {
            throw IllegalArgumentException("La venta ${sale.numeroVenta} ya tiene un comprobante emitido")
        }

        // Solicitar CAE a AFIP
        val afipResponse: AfipResponse = afipService.solicitarCAE(
            sale = sale,
            tipoComprobante = config.tipoComprobante,
            puntoVenta = config.puntoVenta
        )
        
        // Verificar que la respuesta sea exitosa
        if (!afipResponse.isApproved()) {
            throw IllegalStateException("Error al generar comprobante online: ${afipResponse.getObservacionesResumen()}")
        }
        
        // Crear y guardar el comprobante
        val comprobante = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = config.tipoComprobante,
            puntoVenta = config.puntoVenta,
            ventaId = ventaId
        ).withCAE(
            cae = afipResponse.cae,
            estado = afipResponse.getEstadoDescriptivo()
        )
        
        val comprobanteId = saleRepository.saveComprobante(comprobante)
        
        // Marcar la venta como comprobante emitido
        saleRepository.updateComprobanteEmitido(ventaId, true)
        
        return comprobanteId
    }
    
    override suspend fun generarComprobanteOffline(
        ventaId: Id,
        tipoComprobante: String?,
        puntoVenta: Int?
    ): Id {
        // Resolver configuración con valores por defecto
        val config = ComprobanteConfigurationService.resolveConfig(
            puntoVenta = puntoVenta,
            tipoComprobante = tipoComprobante
        )

        // Validar que la venta existe
        val sale = saleRepository.findSaleById(ventaId)
            ?: throw IllegalArgumentException("Venta ${ventaId.value} no encontrada")

        // Validar que la venta no tiene comprobante ya emitido
        if (sale.comprobanteEmitido) {
            throw IllegalArgumentException("La venta ${sale.numeroVenta} ya tiene un comprobante emitido")
        }

        // Crear comprobante con CAEA
        val afipResponse: AfipResponse = afipService.crearComprobanteConCAEA(
            sale = sale,
            tipoComprobante = config.tipoComprobante,
            puntoVenta = config.puntoVenta
        )
        
        // Verificar que la respuesta sea exitosa
        if (!afipResponse.isApproved()) {
            throw IllegalStateException("Error al generar comprobante offline: ${afipResponse.getObservacionesResumen()}")
        }
        
        // Crear y guardar el comprobante
        val comprobante = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = config.tipoComprobante,
            puntoVenta = config.puntoVenta,
            ventaId = ventaId
        ).withCAE(
            cae = afipResponse.cae,
            estado = afipResponse.getEstadoDescriptivo()
        )
        
        val comprobanteId = saleRepository.saveComprobante(comprobante)
        
        // Marcar la venta como comprobante emitido
        saleRepository.updateComprobanteEmitido(ventaId, true)
        
        return comprobanteId
    }
    
    override fun buscarComprobantesPorVenta(ventaId: Id): List<ComprobanteInfo> {
        val comprobantes = saleRepository.findComprobantesByVentaId(ventaId)
        return comprobantes.map { comprobante ->
            ComprobanteInfo(
                id = comprobante.id ?: throw IllegalStateException("Comprobante sin ID"),
                ventaId = comprobante.venta,
                tipoComprobante = comprobante.tipoComprobante,
                puntoVenta = comprobante.puntoVenta,
                numeroComprobante = comprobante.numeroComprobante,
                cae = comprobante.cae,
                estado = comprobante.estado,
                fechaEmision = comprobante.fechaEmision.format(dateFormatter),
                montoTotal = decimalFormatter.format(comprobante.impTotal)
            )
        }
    }
    
    override fun buscarComprobantePorNumero(puntoVenta: Int, numeroComprobante: Int): ComprobanteInfo? {
        val comprobante = saleRepository.findComprobanteByNumero(puntoVenta, numeroComprobante)
            ?: return null
        
        return ComprobanteInfo(
            id = comprobante.id ?: throw IllegalStateException("Comprobante sin ID"),
            ventaId = comprobante.venta,
            tipoComprobante = comprobante.tipoComprobante,
            puntoVenta = comprobante.puntoVenta,
            numeroComprobante = comprobante.numeroComprobante,
            cae = comprobante.cae,
            estado = comprobante.estado,
            fechaEmision = comprobante.fechaEmision.format(dateFormatter),
            montoTotal = decimalFormatter.format(comprobante.impTotal)
        )
    }
    
    override fun contarVentasSinComprobante(): Int {
        return saleRepository.countSalesWithoutComprobante()
    }
    
    override fun obtenerVentasSinComprobante(limit: Int): List<VentaSinComprobanteInfo> {
        val ventas = saleRepository.findSalesWithoutComprobante(limit)
        return ventas.map { venta ->
            VentaSinComprobanteInfo(
                ventaId = venta.id ?: throw IllegalStateException("Venta sin ID"),
                numeroVenta = venta.numeroVenta,
                fechaVenta = venta.fechaVenta.format(dateFormatter),
                clienteNombre = venta.cliente?.nombre,
                usuarioNombre = venta.usuario.nombreDisplay,
                montoTotal = decimalFormatter.format(venta.montoTotal),
                medioPago = venta.medioPago
            )
        }
    }
}
