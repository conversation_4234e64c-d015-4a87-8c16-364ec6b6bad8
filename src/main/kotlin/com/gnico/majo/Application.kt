package com.gnico.majo

import com.gnico.majo.adapter.controller.rest.ProductoController
import com.gnico.majo.adapter.controller.rest.SaleController
import com.gnico.majo.adapter.controller.rest.ExternalProductController
import com.gnico.majo.adapter.controller.rest.UsuarioController
import com.gnico.majo.adapter.controller.rest.ComprobanteController
import com.gnico.majo.adapter.controller.rest.PrintController
import com.gnico.majo.infrastructure.routes.configureProductoRoutes
import com.gnico.majo.infrastructure.routes.configureSaleRoutes
import com.gnico.majo.infrastructure.routes.configureExternalProductRoutes
import com.gnico.majo.infrastructure.routes.configureUsuarioRoutes
import com.gnico.majo.infrastructure.routes.configureComprobanteRoutes
import com.gnico.majo.infrastructure.routes.configurePrintRoutes
import com.gnico.majo.infrastructure.config.appModule
import com.gnico.majo.infrastructure.startup.ApplicationStartup
import io.ktor.serialization.kotlinx.json.json
import io.ktor.server.application.*
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.plugins.cors.routing.CORS
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import org.koin.core.context.stopKoin
import org.koin.ktor.plugin.Koin
import org.koin.ktor.ext.inject
import org.koin.logger.slf4jLogger


fun main(args: Array<String>) {
    io.ktor.server.netty.EngineMain.main(args)
}

fun Application.module() {
    install(ContentNegotiation) {
        json()
    }

    install(CORS) {
        allowMethod(HttpMethod.Options)
        allowMethod(HttpMethod.Put)
        allowMethod(HttpMethod.Delete)
        allowMethod(HttpMethod.Patch)
        allowHeader(HttpHeaders.Authorization)
        allowHeader(HttpHeaders.ContentType)
        // Allow requests from your frontend
        allowHost("localhost:5173")
        allowHost("127.0.0.1:5173")
        // Allow credentials if needed
        allowCredentials = true
    }

    install(Koin) {
        slf4jLogger()
        modules(appModule)
    }

    // Inicializar aplicación con validación de credenciales AFIP
    val applicationStartup by inject<ApplicationStartup>()
    val startupResult = applicationStartup.initialize()

    if (!startupResult.success) {
        log.error("❌ Error crítico durante la inicialización: ${startupResult.error}")
        // La aplicación continuará pero con funcionalidad limitada
    }

    val saleController by inject<SaleController>()
    configureSaleRoutes(saleController)

    val productoController by inject<ProductoController>()
    configureProductoRoutes(productoController)

    val externalProductController by inject<ExternalProductController>()
    configureExternalProductRoutes(externalProductController)

    val usuarioController by inject<UsuarioController>()
    configureUsuarioRoutes(usuarioController)

    val comprobanteController by inject<ComprobanteController>()
    configureComprobanteRoutes(comprobanteController)

    val printController by inject<PrintController>()
    configurePrintRoutes(printController)

    monitor.subscribe(ApplicationStopping) {
        applicationStartup.shutdown()
        stopKoin()
    }
}

