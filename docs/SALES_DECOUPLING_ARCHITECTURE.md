# Arquitectura Desacoplada de Ventas

## Resumen

Se ha implementado una arquitectura desacoplada para el sistema de ventas que permite:

1. **Comprobantes desfasados**: Generar comprobantes fiscales sobre ventas ya realizadas y almacenadas
2. **Reimpresión**: Imprimir tickets de ventas y comprobantes ya procesados

## Arquitectura Anterior vs Nueva

### Antes (Acoplada)
```
SaleService.createSale() -> [Venta + Facturación + Impresión] (todo junto)
```

### Ahora (Desacoplada)
```
SaleService.createSale() -> Solo crear venta
ComprobanteService -> Manejar facturación desfasada
PrintService -> Manejar impresión/reimpresión
```

## Nuevos Servicios

### 1. ComprobanteService
**Responsabilidad**: Manejo de comprobantes fiscales de forma independiente

**Métodos principales**:
- `generarComprobanteOnline(ventaId, tipoComprobante, puntoVenta)` - CAE
- `generarComprobanteOffline(ventaId, tipoComprobante, puntoVenta)` - CAEA
- `buscarComprobantesPorVenta(ventaId)` - Consultar comprobantes de una venta
- `obtenerVentasSinComprobante(limit)` - Ventas pendientes de facturar

### 2. PrintService
**Responsabilidad**: Manejo de impresión y reimpresión

**Métodos principales**:
- `imprimirTicketVenta(ventaId)` - Ticket genérico de venta
- `imprimirTicketComprobante(comprobanteId)` - Ticket de comprobante
- `imprimirComprobanteFiscal(comprobanteId)` - Comprobante fiscal completo
- `buscarVentasParaReimpresion(...)` - Buscar ventas para reimprimir
- `buscarComprobantesParaReimpresion(...)` - Buscar comprobantes para reimprimir

### 3. SaleService (Extendido)
**Nuevos métodos de consulta**:
- `findSaleById(ventaId)` - Buscar venta por ID
- `findSaleByNumero(numeroVenta)` - Buscar venta por número
- `findSalesByDateRange(fechaDesde, fechaHasta)` - Buscar por rango de fechas
- `findSalesByUsuario(username)` - Buscar ventas de un usuario

## Nuevos Controladores REST

### ComprobanteController
- `POST /comprobantes/online` - Generar comprobante online
- `POST /comprobantes/offline` - Generar comprobante offline
- `GET /comprobantes/venta/{ventaId}` - Comprobantes de una venta
- `GET /comprobantes/{puntoVenta}/{numero}` - Buscar por número
- `GET /ventas-sin-comprobante` - Ventas pendientes de facturar

### PrintController
- `POST /print/ticket-venta/{ventaId}` - Imprimir ticket de venta
- `POST /print/ticket-comprobante/{comprobanteId}` - Imprimir ticket de comprobante
- `POST /print/comprobante-fiscal/{comprobanteId}` - Imprimir comprobante fiscal
- `POST /print/buscar-ventas` - Buscar ventas para reimpresión
- `POST /print/buscar-comprobantes` - Buscar comprobantes para reimpresión

## Repositorio Extendido

### SaleRepositoryPort (Nuevos métodos)
```kotlin
// Búsqueda de ventas
fun findSaleByNumeroVenta(numeroVenta: String): Sale?
fun findSalesByDateRange(startDate: LocalDateTime, endDate: LocalDateTime): List<Sale>
fun findSalesByUsuario(username: String): List<Sale>
fun findSalesByCliente(clienteId: Id): List<Sale>

// Búsqueda de comprobantes
fun findComprobantesByVentaId(ventaId: Id): List<Comprobante>
fun findComprobanteByNumero(puntoVenta: Int, numeroComprobante: Int): Comprobante?

// Estadísticas
fun countSalesWithoutComprobante(): Int
fun findSalesWithoutComprobante(limit: Int = 100): List<Sale>
```

## Casos de Uso

### 1. Comprobante Desfasado
```kotlin
// 1. Buscar venta existente
val venta = saleService.findSaleByNumero("V-12345678")

// 2. Generar comprobante posteriormente
val comprobanteId = comprobanteService.generarComprobanteOnline(
    ventaId = venta.id,
    tipoComprobante = "FACTURA_B",
    puntoVenta = 1
)

// 3. Imprimir si es necesario
printService.imprimirTicketComprobante(comprobanteId)
```

### 2. Reimpresión de Documentos
```kotlin
// Buscar ventas para reimprimir
val ventas = printService.buscarVentasParaReimpresion(
    fechaDesde = "2024-01-01 00:00:00",
    fechaHasta = "2024-01-31 23:59:59",
    usuario = "vendedor1"
)

// Reimprimir ticket de una venta específica
printService.imprimirTicketVenta(ventaId)
```

### 3. Procesamiento de Ventas Pendientes
```kotlin
// Obtener ventas sin comprobante
val ventasPendientes = comprobanteService.obtenerVentasSinComprobante(50)

// Procesar cada venta
ventasPendientes.forEach { venta ->
    comprobanteService.generarComprobanteOnline(
        ventaId = venta.ventaId,
        tipoComprobante = "FACTURA_B",
        puntoVenta = 1
    )
}
```

## Beneficios de la Nueva Arquitectura

1. **Flexibilidad**: Facturación e impresión independientes de la venta
2. **Recuperación**: Procesar ventas que fallaron en facturación inicial
3. **Reimpresión**: Fácil reimpresión de documentos históricos
4. **Mantenibilidad**: Responsabilidades claramente separadas
5. **Escalabilidad**: Cada servicio puede evolucionar independientemente

## Compatibilidad

- El método `SaleService.createSale()` mantiene su comportamiento original
- El método `SaleService.createComprobante()` está marcado como `@Deprecated`
- Todos los cambios son retrocompatibles

## Configuración de Koin

Los nuevos servicios se han agregado al módulo de dependencias:

```kotlin
single<ComprobanteService> { ComprobanteServiceImpl(get(), get()) }
single<PrintService> { PrintServiceImpl(get(), get()) }
single { ComprobanteController(get()) }
single { PrintController(get()) }
```
