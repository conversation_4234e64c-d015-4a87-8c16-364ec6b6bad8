# API Endpoints - Sistema de Ventas Desacoplado

## Endpoints de Comprobantes

### Generar Comprobantes

#### POST `/api/comprobantes/online`
Genera un comprobante fiscal online (CAE) para una venta existente.

**Request Body:**
```json
{
  "ventaId": 123,
  "tipoComprobante": "FACTURA_B",  // Opcional, default desde .env
  "puntoVenta": 1                  // Opcional, default desde .env
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Comprobante online generado exitosamente",
  "comprobanteId": 456
}
```

#### POST `/api/comprobantes/offline`
Genera un comprobante fiscal offline (CAEA) para una venta existente.

**Request Body:** (Igual que `/online`)
**Response:** (Igual que `/online`)

### Consultar Comprobantes

#### GET `/api/comprobantes/venta/{ventaId}`
Busca todos los comprobantes asociados a una venta.

**Response (200 OK):**
```json
[
  {
    "id": 456,
    "ventaId": 123,
    "tipoComprobante": "FACTURA_B",
    "puntoVenta": 1,
    "numeroComprobante": 12345,
    "cae": "74123456789012",
    "estado": "APROBADO",
    "fechaEmision": "15/01/2024 14:30",
    "montoTotal": "1,250.00"
  }
]
```

#### GET `/api/comprobantes/{puntoVenta}/{numeroComprobante}`
Busca un comprobante específico por su número.

**Ejemplo:** `GET /api/comprobantes/1/12345`

**Response (200 OK):** (Mismo formato que arriba, objeto único)

### Estadísticas

#### GET `/api/comprobantes/estadisticas/ventas-sin-comprobante`
Obtiene el número de ventas que no tienen comprobante emitido.

**Response (200 OK):**
```json
{
  "totalVentasSinComprobante": 25
}
```

#### GET `/api/comprobantes/ventas-sin-comprobante?limit=50`
Lista de ventas sin comprobante para procesamiento.

**Query Parameters:**
- `limit` (opcional): Número máximo de resultados (1-1000, default: 100)

**Response (200 OK):**
```json
[
  {
    "ventaId": 123,
    "numeroVenta": "V-12345678",
    "fechaVenta": "15/01/2024 14:30",
    "clienteNombre": "Cliente Ejemplo",
    "usuarioNombre": "Vendedor 1",
    "montoTotal": "1,250.00",
    "medioPago": "EFECTIVO"
  }
]
```

## Endpoints de Impresión

### Imprimir Documentos

#### POST `/api/print/ticket-venta/{ventaId}`
Imprime un ticket genérico para una venta (sin comprobante fiscal).

**Ejemplo:** `POST /api/print/ticket-venta/123`

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Ticket de venta impreso exitosamente"
}
```

#### POST `/api/print/ticket-comprobante/{comprobanteId}`
Imprime un ticket de comprobante fiscal.

**Ejemplo:** `POST /api/print/ticket-comprobante/456`

**Response:** (Mismo formato que arriba)

#### POST `/api/print/comprobante-fiscal/{comprobanteId}`
Imprime un comprobante fiscal completo (formato oficial).

**Ejemplo:** `POST /api/print/comprobante-fiscal/456`

**Response:** (Mismo formato que arriba)

### Búsqueda para Reimpresión

#### POST `/api/print/buscar-ventas`
Busca ventas por diferentes criterios para reimpresión.

**Request Body:**
```json
{
  "numeroVenta": "V-12345678",        // Opcional
  "fechaDesde": "2024-01-01 00:00:00", // Opcional
  "fechaHasta": "2024-01-31 23:59:59", // Opcional
  "usuario": "vendedor1"               // Opcional
}
```

**Response (200 OK):**
```json
[
  {
    "ventaId": 123,
    "numeroVenta": "V-12345678",
    "fechaVenta": "15/01/2024 14:30",
    "clienteNombre": "Cliente Ejemplo",
    "usuarioNombre": "Vendedor 1",
    "montoTotal": "1,250.00",
    "medioPago": "EFECTIVO",
    "tieneComprobante": true,
    "comprobantes": [
      {
        "comprobanteId": 456,
        "tipoComprobante": "FACTURA_B",
        "puntoVenta": 1,
        "numeroComprobante": 12345,
        "estado": "APROBADO"
      }
    ]
  }
]
```

#### POST `/api/print/buscar-comprobantes`
Busca comprobantes por diferentes criterios para reimpresión.

**Request Body:**
```json
{
  "puntoVenta": 1,           // Opcional
  "numeroComprobante": 12345, // Opcional
  "fechaDesde": "2024-01-01 00:00:00", // Opcional
  "fechaHasta": "2024-01-31 23:59:59"  // Opcional
}
```

**Response (200 OK):**
```json
[
  {
    "comprobanteId": 456,
    "ventaId": 123,
    "numeroVenta": "V-12345678",
    "tipoComprobante": "FACTURA_B",
    "puntoVenta": 1,
    "numeroComprobante": 12345,
    "cae": "74123456789012",
    "fechaEmision": "15/01/2024 14:30",
    "clienteNombre": "Cliente Ejemplo",
    "usuarioNombre": "Vendedor 1",
    "montoTotal": "1,250.00",
    "estado": "APROBADO"
  }
]
```

### Búsqueda Directa

#### GET `/api/print/ventas/{numeroVenta}`
Busca una venta específica para reimpresión.

**Ejemplo:** `GET /api/print/ventas/V-12345678`

**Response (200 OK):** (Primer elemento del array de búsqueda de ventas)

#### GET `/api/print/comprobantes/{puntoVenta}/{numeroComprobante}`
Busca un comprobante específico para reimpresión.

**Ejemplo:** `GET /api/print/comprobantes/1/12345`

**Response (200 OK):** (Primer elemento del array de búsqueda de comprobantes)

## Códigos de Error

### 400 Bad Request
- Parámetros faltantes o inválidos
- Formato de fecha incorrecto
- Límites fuera de rango

### 404 Not Found
- Venta no encontrada
- Comprobante no encontrado

### 409 Conflict
- Venta ya tiene comprobante emitido
- Error de estado en AFIP

### 500 Internal Server Error
- Error interno del servidor
- Error de comunicación con AFIP
- Error de impresión

## Ejemplos de Uso con cURL

### Generar comprobante con valores por defecto
```bash
curl -X POST http://localhost:8080/api/comprobantes/online \
  -H "Content-Type: application/json" \
  -d '{"ventaId": 123}'
```

### Buscar ventas por fecha
```bash
curl -X POST http://localhost:8080/api/print/buscar-ventas \
  -H "Content-Type: application/json" \
  -d '{
    "fechaDesde": "2024-01-01 00:00:00",
    "fechaHasta": "2024-01-31 23:59:59"
  }'
```

### Imprimir ticket de venta
```bash
curl -X POST http://localhost:8080/api/print/ticket-venta/123
```

### Obtener estadísticas
```bash
curl http://localhost:8080/api/comprobantes/estadisticas/ventas-sin-comprobante
```
