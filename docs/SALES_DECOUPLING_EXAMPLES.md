# Ejemplos de Uso - Sistema de Ventas Desacoplado

## Escenarios de Uso Prácticos

### 1. Venta Normal (Comportamiento Original)

```kotlin
// Crear venta con facturación e impresión inmediata
val ventaId = saleService.createSale(
    clienteId = 123,
    vendedor = "vendedor1",
    itemsRequest = listOf(
        SaleItemRequest(
            productoId = 1001,
            cantidad = 2.0,
            precioUnitario = 150.00,
            // ... otros campos
        )
    ),
    medioPago = "EFECTIVO",
    imprimirTicket = true,
    facturaOnline = true,
    facturaOffline = false
)
```

### 2. Venta Solo Registro (Sin Facturación Inmediata)

```kotlin
// Crear venta sin facturar (para procesar después)
val ventaId = saleService.createSale(
    clienteId = 123,
    vendedor = "vendedor1",
    itemsRequest = items,
    medioPago = "EFECTIVO",
    imprimirTicket = true,      // Solo ticket genérico
    facturaOnline = false,      // Sin facturación
    facturaOffline = false
)

// Más tarde, generar comprobante desfasado
val comprobanteId = comprobanteService.generarComprobanteOnline(
    ventaId = ventaId,
    tipoComprobante = "FACTURA_B",
    puntoVenta = 1
)
```

### 3. Procesamiento Masivo de Ventas Pendientes

```kotlin
// Obtener ventas sin comprobante
val ventasPendientes = comprobanteService.obtenerVentasSinComprobante(100)

println("Procesando ${ventasPendientes.size} ventas pendientes...")

ventasPendientes.forEach { venta ->
    try {
        val comprobanteId = comprobanteService.generarComprobanteOnline(
            ventaId = venta.ventaId,
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1
        )
        
        println("✓ Comprobante generado para venta ${venta.numeroVenta}")
        
        // Opcionalmente imprimir
        printService.imprimirTicketComprobante(comprobanteId)
        
    } catch (e: Exception) {
        println("✗ Error en venta ${venta.numeroVenta}: ${e.message}")
    }
}
```

### 4. Reimpresión de Documentos

```kotlin
// Buscar ventas por fecha para reimprimir
val ventas = printService.buscarVentasParaReimpresion(
    fechaDesde = "2024-01-15 00:00:00",
    fechaHasta = "2024-01-15 23:59:59",
    usuario = null,
    numeroVenta = null
)

println("Encontradas ${ventas.size} ventas del 15/01/2024")

ventas.forEach { venta ->
    println("Venta: ${venta.numeroVenta} - ${venta.montoTotal}")
    
    if (venta.tieneComprobante) {
        // Reimprimir comprobante fiscal
        venta.comprobantes.forEach { comprobante ->
            printService.imprimirComprobanteFiscal(comprobante.comprobanteId)
        }
    } else {
        // Reimprimir solo ticket de venta
        printService.imprimirTicketVenta(venta.ventaId)
    }
}
```

### 5. Búsqueda y Reimpresión por Número de Comprobante

```kotlin
// Buscar comprobante específico
val comprobante = comprobanteService.buscarComprobantePorNumero(
    puntoVenta = 1,
    numeroComprobante = 12345
)

if (comprobante != null) {
    println("Comprobante encontrado: ${comprobante.tipoComprobante}")
    println("CAE: ${comprobante.cae}")
    println("Estado: ${comprobante.estado}")
    
    // Reimprimir
    printService.imprimirComprobanteFiscal(comprobante.id)
} else {
    println("Comprobante no encontrado")
}
```

### 6. Consulta de Ventas por Usuario

```kotlin
// Obtener todas las ventas de un vendedor
val ventasVendedor = saleService.findSalesByUsuario("vendedor1")

println("Ventas de vendedor1: ${ventasVendedor.size}")

ventasVendedor.forEach { venta ->
    println("${venta.numeroVenta} - ${venta.fechaVenta} - $${venta.montoTotal}")
    
    // Ver comprobantes asociados
    val comprobantes = comprobanteService.buscarComprobantesPorVenta(venta.id!!)
    if (comprobantes.isNotEmpty()) {
        comprobantes.forEach { comp ->
            println("  └─ ${comp.tipoComprobante} ${comp.puntoVenta}-${comp.numeroComprobante}")
        }
    } else {
        println("  └─ Sin comprobantes")
    }
}
```

### 7. Recuperación de Errores de Facturación

```kotlin
// Escenario: Una venta se creó pero falló la facturación por problemas de conectividad

// 1. Buscar la venta
val venta = saleService.findSaleByNumero("V-12345678")
if (venta != null && !venta.comprobanteEmitido) {
    
    try {
        // 2. Intentar facturación online
        val comprobanteId = comprobanteService.generarComprobanteOnline(
            ventaId = venta.id!!,
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1
        )
        
        println("✓ Facturación recuperada exitosamente")
        
        // 3. Imprimir comprobante
        printService.imprimirTicketComprobante(comprobanteId)
        
    } catch (e: Exception) {
        println("✗ Facturación online falló, intentando offline...")
        
        try {
            // 4. Fallback a facturación offline
            val comprobanteId = comprobanteService.generarComprobanteOffline(
                ventaId = venta.id!!,
                tipoComprobante = "FACTURA_B",
                puntoVenta = 1
            )
            
            println("✓ Facturación offline exitosa")
            printService.imprimirTicketComprobante(comprobanteId)
            
        } catch (e2: Exception) {
            println("✗ Ambas facturaciones fallaron: ${e2.message}")
        }
    }
}
```

### 8. Estadísticas y Monitoreo

```kotlin
// Obtener estadísticas de ventas sin comprobante
val stats = comprobanteController.obtenerEstadisticasVentasSinComprobante()
println("Ventas pendientes de facturar: ${stats.totalVentasSinComprobante}")

if (stats.totalVentasSinComprobante > 0) {
    // Obtener detalles de las primeras 10
    val ventasPendientes = comprobanteService.obtenerVentasSinComprobante(10)
    
    println("\nPrimeras 10 ventas pendientes:")
    ventasPendientes.forEach { venta ->
        println("${venta.numeroVenta} - ${venta.fechaVenta} - ${venta.usuarioNombre} - $${venta.montoTotal}")
    }
}
```

## Endpoints REST Disponibles

### ComprobanteController
```http
POST /api/comprobantes/online
{
  "ventaId": 123,
  "tipoComprobante": "FACTURA_B",
  "puntoVenta": 1
}

POST /api/comprobantes/offline
{
  "ventaId": 123,
  "tipoComprobante": "FACTURA_B", 
  "puntoVenta": 1
}

GET /api/comprobantes/venta/123
GET /api/comprobantes/1/12345
GET /api/ventas-sin-comprobante?limit=50
GET /api/estadisticas/ventas-sin-comprobante
```

### PrintController
```http
POST /api/print/ticket-venta/123
POST /api/print/ticket-comprobante/456
POST /api/print/comprobante-fiscal/456

POST /api/print/buscar-ventas
{
  "numeroVenta": "V-12345678",
  "fechaDesde": "2024-01-01 00:00:00",
  "fechaHasta": "2024-01-31 23:59:59",
  "usuario": "vendedor1"
}

POST /api/print/buscar-comprobantes
{
  "puntoVenta": 1,
  "numeroComprobante": 12345
}
```

## Beneficios Demostrados

1. **Flexibilidad**: Facturación independiente de la venta
2. **Recuperación**: Procesar ventas que fallaron inicialmente
3. **Reimpresión**: Fácil acceso a documentos históricos
4. **Monitoreo**: Visibilidad de ventas pendientes
5. **Escalabilidad**: Procesamiento masivo de documentos
